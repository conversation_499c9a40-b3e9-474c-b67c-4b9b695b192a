{"version": 3, "sources": ["../../@radix-ui/react-collapsible/src/Collapsible.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Collapsible\n * -----------------------------------------------------------------------------------------------*/\n\nconst COLLAPSIBLE_NAME = 'Collapsible';\n\ntype ScopedProps<P> = P & { __scopeCollapsible?: Scope };\nconst [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\n\ntype CollapsibleContextValue = {\n  contentId: string;\n  disabled?: boolean;\n  open: boolean;\n  onOpenToggle(): void;\n};\n\nconst [CollapsibleProvider, useCollapsibleContext] =\n  createCollapsibleContext<CollapsibleContextValue>(COLLAPSIBLE_NAME);\n\ntype CollapsibleElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface CollapsibleProps extends PrimitiveDivProps {\n  defaultOpen?: boolean;\n  open?: boolean;\n  disabled?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst Collapsible = React.forwardRef<CollapsibleElement, CollapsibleProps>(\n  (props: ScopedProps<CollapsibleProps>, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n\n    const [open = false, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen,\n      onChange: onOpenChange,\n    });\n\n    return (\n      <CollapsibleProvider\n        scope={__scopeCollapsible}\n        disabled={disabled}\n        contentId={useId()}\n        open={open}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      >\n        <Primitive.div\n          data-state={getState(open)}\n          data-disabled={disabled ? '' : undefined}\n          {...collapsibleProps}\n          ref={forwardedRef}\n        />\n      </CollapsibleProvider>\n    );\n  }\n);\n\nCollapsible.displayName = COLLAPSIBLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CollapsibleTrigger';\n\ntype CollapsibleTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CollapsibleTriggerProps extends PrimitiveButtonProps {}\n\nconst CollapsibleTrigger = React.forwardRef<CollapsibleTriggerElement, CollapsibleTriggerProps>(\n  (props: ScopedProps<CollapsibleTriggerProps>, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-controls={context.contentId}\n        aria-expanded={context.open || false}\n        data-state={getState(context.open)}\n        data-disabled={context.disabled ? '' : undefined}\n        disabled={context.disabled}\n        {...triggerProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nCollapsibleTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'CollapsibleContent';\n\ntype CollapsibleContentElement = CollapsibleContentImplElement;\ninterface CollapsibleContentProps extends Omit<CollapsibleContentImplProps, 'present'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CollapsibleContent = React.forwardRef<CollapsibleContentElement, CollapsibleContentProps>(\n  (props: ScopedProps<CollapsibleContentProps>, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return (\n      <Presence present={forceMount || context.open}>\n        {({ present }) => (\n          <CollapsibleContentImpl {...contentProps} ref={forwardedRef} present={present} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nCollapsibleContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype CollapsibleContentImplElement = React.ElementRef<typeof Primitive.div>;\ninterface CollapsibleContentImplProps extends PrimitiveDivProps {\n  present: boolean;\n}\n\nconst CollapsibleContentImpl = React.forwardRef<\n  CollapsibleContentImplElement,\n  CollapsibleContentImplProps\n>((props: ScopedProps<CollapsibleContentImplProps>, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef<CollapsibleContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef<number | undefined>(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef<number | undefined>(0);\n  const width = widthRef.current;\n  // when opening we want it to immediately open to retrieve dimensions\n  // when closing we delay `present` to retrieve dimensions before closing\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef<Record<string, string>>();\n\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName,\n      };\n      // block any animations/transitions so the element renders at its full dimensions\n      node.style.transitionDuration = '0s';\n      node.style.animationName = 'none';\n\n      // get width and height from full dimensions\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n\n      // kick off any animations/transitions that were originally set up if it isn't the initial mount\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n        node.style.animationName = originalStylesRef.current.animationName;\n      }\n\n      setIsPresent(present);\n    }\n    /**\n     * depends on `context.open` because it will change to `false`\n     * when a close is triggered but `present` will be `false` on\n     * animation end (so when close finishes). This allows us to\n     * retrieve the dimensions *before* closing.\n     */\n  }, [context.open, present]);\n\n  return (\n    <Primitive.div\n      data-state={getState(context.open)}\n      data-disabled={context.disabled ? '' : undefined}\n      id={context.contentId}\n      hidden={!isOpen}\n      {...contentProps}\n      ref={composedRefs}\n      style={{\n        [`--radix-collapsible-content-height` as any]: height ? `${height}px` : undefined,\n        [`--radix-collapsible-content-width` as any]: width ? `${width}px` : undefined,\n        ...props.style,\n      }}\n    >\n      {isOpen && children}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Collapsible;\nconst Trigger = CollapsibleTrigger;\nconst Content = CollapsibleContent;\n\nexport {\n  createCollapsibleScope,\n  //\n  Collapsible,\n  CollapsibleTrigger,\n  CollapsibleContent,\n  //\n  Root,\n  Trigger,\n  Content,\n};\nexport type { CollapsibleProps, CollapsibleTriggerProps, CollapsibleContentProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAiEf,yBAAA;AAjDR,IAAM,mBAAmB;AAGzB,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,gBAAgB;AAS9F,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,yBAAkD,gBAAgB;AAWpE,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM;MACJ;MACA,MAAM;MACN;MACA;MACA;MACA,GAAG;IACL,IAAI;AAEJ,UAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;MACnD,MAAM;MACN,aAAa;MACb,UAAU;IACZ,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,WAAW,MAAM;QACjB;QACA,cAAoB,kBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;QAEjF,cAAA;UAAC,UAAU;UAAV;YACC,cAAY,SAAS,IAAI;YACzB,iBAAe,WAAW,KAAK;YAC9B,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,iBAAe,QAAQ;QACvB,iBAAe,QAAQ,QAAQ;QAC/B,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,WAAW,KAAK;QACvC,UAAU,QAAQ;QACjB,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;MAAA;IACnE;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,eAAe;AAWrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,YAAY,GAAG,aAAa,IAAI;AACxC,UAAM,UAAU,sBAAsB,cAAc,MAAM,kBAAkB;AAC5E,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACtC,UAAA,CAAC,EAAE,QAAQ,UACV,wBAAC,wBAAA,EAAwB,GAAG,cAAc,KAAK,cAAc,QAAA,CAAkB,EAAA,CAEnF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AASjC,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,oBAAoB,SAAS,UAAU,GAAG,aAAa,IAAI;AACnE,QAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,OAAO;AACxD,QAAM,MAAY,aAAsC,IAAI;AAC5D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,YAAkB,aAA2B,CAAC;AACpD,QAAM,SAAS,UAAU;AACzB,QAAM,WAAiB,aAA2B,CAAC;AACnD,QAAM,QAAQ,SAAS;AAGvB,QAAM,SAAS,QAAQ,QAAQ;AAC/B,QAAM,+BAAqC,aAAO,MAAM;AACxD,QAAM,oBAA0B,aAA+B;AAEzD,EAAA,gBAAU,MAAM;AACpB,UAAM,MAAM,sBAAsB,MAAO,6BAA6B,UAAU,KAAM;AACtF,WAAO,MAAM,qBAAqB,GAAG;EACvC,GAAG,CAAC,CAAC;AAEL,mBAAgB,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,wBAAkB,UAAU,kBAAkB,WAAW;QACvD,oBAAoB,KAAK,MAAM;QAC/B,eAAe,KAAK,MAAM;MAC5B;AAEA,WAAK,MAAM,qBAAqB;AAChC,WAAK,MAAM,gBAAgB;AAG3B,YAAM,OAAO,KAAK,sBAAsB;AACxC,gBAAU,UAAU,KAAK;AACzB,eAAS,UAAU,KAAK;AAGxB,UAAI,CAAC,6BAA6B,SAAS;AACzC,aAAK,MAAM,qBAAqB,kBAAkB,QAAQ;AAC1D,aAAK,MAAM,gBAAgB,kBAAkB,QAAQ;MACvD;AAEA,mBAAa,OAAO;IACtB;EAOF,GAAG,CAAC,QAAQ,MAAM,OAAO,CAAC;AAE1B,aACE;IAAC,UAAU;IAAV;MACC,cAAY,SAAS,QAAQ,IAAI;MACjC,iBAAe,QAAQ,WAAW,KAAK;MACvC,IAAI,QAAQ;MACZ,QAAQ,CAAC;MACR,GAAG;MACJ,KAAK;MACL,OAAO;QACL,CAAC,oCAA2C,GAAG,SAAS,GAAG,MAAM,OAAO;QACxE,CAAC,mCAA0C,GAAG,QAAQ,GAAG,KAAK,OAAO;QACrE,GAAG,MAAM;MACX;MAEC,UAAA,UAAU;IAAA;EACb;AAEJ,CAAC;AAID,SAAS,SAAS,MAAgB;AAChC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;", "names": []}