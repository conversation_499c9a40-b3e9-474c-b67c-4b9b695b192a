{"name": "@fast-csv/parse", "version": "4.3.6", "description": "fast-csv parsing package", "keywords": ["csv", "parse", "fast-csv", "parser"], "author": "doug-martin <<EMAIL>>", "homepage": "http://c2fo.github.com/fast-csv/packages/parse", "license": "MIT", "main": "build/src/index.js", "types": "build/src/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["build/src/**"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/C2FO/fast-csv.git", "directory": "packages/parse"}, "scripts": {"prepublishOnly": "npm run build", "build": "npm run clean && npm run compile", "clean": "rm -rf ./build && rm -rf tsconfig.tsbuildinfo", "compile": "tsc"}, "bugs": {"url": "https://github.com/C2FO/fast-csv/issues"}, "dependencies": {"@types/node": "^14.0.1", "lodash.escaperegexp": "^4.1.2", "lodash.groupby": "^4.6.0", "lodash.isfunction": "^3.0.9", "lodash.isnil": "^4.0.0", "lodash.isundefined": "^3.0.1", "lodash.uniq": "^4.5.0"}, "devDependencies": {"@types/lodash.escaperegexp": "4.1.6", "@types/lodash.groupby": "4.6.6", "@types/lodash.isfunction": "3.0.6", "@types/lodash.isnil": "4.0.6", "@types/lodash.isundefined": "3.0.6", "@types/lodash.partition": "4.6.6", "@types/lodash.uniq": "4.5.6", "@types/sinon": "9.0.9", "lodash.partition": "4.6.0", "sinon": "9.2.1"}, "gitHead": "3dc859edb19924b315051e4c87d6273808a0de73"}