{"version": 3, "file": "Token.js", "sourceRoot": "", "sources": ["../../../src/parser/Token.ts"], "names": [], "mappings": ";;;AAUA,MAAa,KAAK;IAgCd,YAAmB,SAAoB;QACnC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;IACzC,CAAC;IAnCM,MAAM,CAAC,mBAAmB,CAAC,KAAY;QAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5B,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,CAAC;IACtE,CAAC;IAEM,MAAM,CAAC,qBAAqB,CAAC,KAAY,EAAE,aAA4B;QAC1E,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa,CAAC,cAAc,CAAC;IACxD,CAAC;IAEM,MAAM,CAAC,cAAc,CAAC,KAAY,EAAE,aAA4B;QACnE,OAAO,aAAa,CAAC,gBAAgB,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,aAAa,CAAC,OAAO,CAAC;IAC9F,CAAC;IAEM,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAE,aAA4B;QAC3E,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa,CAAC,UAAU,CAAC;IACpD,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,KAAY,EAAE,aAA4B;QACjE,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,CAAC;IAC/C,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,KAAY,EAAE,aAA4B;QACrE,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa,CAAC,SAAS,CAAC;IACnD,CAAC;CAaJ;AArCD,sBAqCC"}