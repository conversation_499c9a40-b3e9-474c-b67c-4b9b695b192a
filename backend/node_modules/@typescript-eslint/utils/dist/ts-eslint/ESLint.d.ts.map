{"version": 3, "file": "ESLint.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/ESLint.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAEvC,OAAO,OAAO,UAAU;IACtB;;;OAGG;gBACS,OAAO,CAAC,EAAE,MAAM,CAAC,aAAa;IAE1C;;;;;;;;;;;OAWG;IACH,sBAAsB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;IACpE;;;;;OAKG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IACjD;;;;OAIG;IACH,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IACpE;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,CACN,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,MAAM,CAAC,eAAe,GAC/B,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAC/B;;;;;;;;;;;;OAYG;IACH,aAAa,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;IAMvD;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU;IACrE;;;;;OAKG;IACH,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAC/D;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;CACjC;AAED,kBAAU,MAAM,CAAC;IACf,UAAiB,aAAa;QAC5B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAC5B;;;;WAIG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;QACtC;;;;;WAKG;QACH,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB;;WAEG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB;;WAEG;QACH,GAAG,CAAC,EAAE,MAAM,CAAC;QACb;;WAEG;QACH,uBAAuB,CAAC,EAAE,OAAO,CAAC;QAClC;;;;;;;WAOG;QACH,UAAU,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAC7B;;;;WAIG;QACH,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC;QAC3D;;WAEG;QACH,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,YAAY,CAAC,EAAE,GAAG,IAAI,CAAC;QACxE;;WAEG;QACH,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB;;;WAGG;QACH,MAAM,CAAC,EAAE,OAAO,CAAC;QACjB;;;WAGG;QACH,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB;;;WAGG;QACH,cAAc,CAAC,EAAE,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC;QACrD;;;WAGG;QACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACnC;;;WAGG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC/C;;;WAGG;QACH,6BAA6B,CAAC,EAAE,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7D;;;;;WAKG;QACH,wBAAwB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;QACzC;;WAEG;QACH,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;QACrB;;;WAGG;QACH,WAAW,CAAC,EAAE,OAAO,CAAC;KACvB;IAED,UAAiB,kBAAkB;QACjC;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QACf;;WAEG;QACH,UAAU,EAAE,MAAM,EAAE,CAAC;KACtB;IAED;;OAEG;IACH,UAAiB,UAAU;QACzB;;WAEG;QACH,UAAU,EAAE,MAAM,CAAC;QACnB;;;WAGG;QACH,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB;;;WAGG;QACH,QAAQ,EAAE,MAAM,CAAC;QACjB;;WAEG;QACH,iBAAiB,EAAE,MAAM,CAAC;QAC1B;;WAEG;QACH,mBAAmB,EAAE,MAAM,CAAC;QAC5B;;WAEG;QACH,QAAQ,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;QAC/B;;WAEG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB;;;WAGG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB;;;;WAIG;QACH,kBAAkB,CAAC,EAAE,qBAAqB,EAAE,CAAC;QAC7C;;WAEG;QACH,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;QAC1C;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;KACtB;IAED,UAAiB,eAAe;QAC9B;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB;;;WAGG;QACH,WAAW,CAAC,EAAE,OAAO,CAAC;KACvB;IAED;;OAEG;IACH,UAAiB,WAAW;QAC1B;;WAEG;QACH,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC;QAC3B;;;WAGG;QACH,SAAS,EAAE,MAAM,GAAG,SAAS,CAAC;QAC9B;;;WAGG;QACH,OAAO,EAAE,MAAM,GAAG,SAAS,CAAC;QAC5B;;;WAGG;QACH,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;QAC5B;;WAEG;QACH,GAAG,EAAE,QAAQ,GAAG,SAAS,CAAC;QAC1B;;WAEG;QACH,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC;QACzB;;WAEG;QACH,OAAO,EAAE,MAAM,CAAC;QAChB;;;WAGG;QACH,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;QACtB;;WAEG;QACH,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC;QAChB;;;;WAIG;QACH,WAAW,EACP;YACE,IAAI,EAAE,MAAM,CAAC;YACb,GAAG,EAAE,QAAQ,CAAC;SACf,EAAE,GACH,SAAS,CAAC;KACf;IAED;;OAEG;IACH,UAAiB,qBAAsB,SAAQ,MAAM,CAAC,WAAW;QAC/D;;WAEG;QACH,YAAY,CAAC,EAAE;YACb;;eAEG;YACH,IAAI,EAAE,MAAM,CAAC;YACb;;eAEG;YACH,aAAa,EAAE,MAAM,CAAC;SACvB,EAAE,CAAC;KACL;IAED;;;;;;;OAOG;IACH,UAAiB,QAAQ;QACvB;;WAEG;QACH,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;KACd;IAED;;OAEG;IACH,UAAiB,SAAS;QACxB;;;WAGG;QACH,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;KACzD;CACF;AAKD,QAAA,MAAM,OAAO,mBAKY,CAAC;AAE1B;;;;;;;GAOG;AACH,cAAM,MAAO,SAAQ,OAAO;CAAG;AAE/B,OAAO,EAAE,MAAM,EAAE,CAAC"}