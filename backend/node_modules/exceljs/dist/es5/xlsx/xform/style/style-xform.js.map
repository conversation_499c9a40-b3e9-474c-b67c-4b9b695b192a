{"version": 3, "file": "style-xform.js", "names": ["BaseXform", "require", "AlignmentXform", "ProtectionXform", "StyleXform", "constructor", "options", "xfId", "map", "alignment", "protection", "tag", "render", "xmlStream", "model", "openNode", "numFmtId", "fontId", "fillId", "borderId", "addAttribute", "closeNode", "parseOpen", "node", "parser", "name", "parseInt", "attributes", "parseText", "text", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/style-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nconst AlignmentXform = require('./alignment-xform');\nconst ProtectionXform = require('./protection-xform');\n\n// <xf numFmtId=\"[numFmtId]\" fontId=\"[fontId]\" fillId=\"[fillId]\" borderId=\"[xf.borderId]\" xfId=\"[xfId]\">\n//   Optional <alignment>\n//   Optional <protection>\n// </xf>\n\n// Style assists translation from style model to/from xlsx\nclass StyleXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.xfId = !!(options && options.xfId);\n    this.map = {\n      alignment: new AlignmentXform(),\n      protection: new ProtectionXform(),\n    };\n  }\n\n  get tag() {\n    return 'xf';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('xf', {\n      numFmtId: model.numFmtId || 0,\n      fontId: model.fontId || 0,\n      fillId: model.fillId || 0,\n      borderId: model.borderId || 0,\n    });\n    if (this.xfId) {\n      xmlStream.addAttribute('xfId', model.xfId || 0);\n    }\n\n    if (model.numFmtId) {\n      xmlStream.addAttribute('applyNumberFormat', '1');\n    }\n    if (model.fontId) {\n      xmlStream.addAttribute('applyFont', '1');\n    }\n    if (model.fillId) {\n      xmlStream.addAttribute('applyFill', '1');\n    }\n    if (model.borderId) {\n      xmlStream.addAttribute('applyBorder', '1');\n    }\n    if (model.alignment) {\n      xmlStream.addAttribute('applyAlignment', '1');\n    }\n    if (model.protection) {\n      xmlStream.addAttribute('applyProtection', '1');\n    }\n\n    /**\n     * Rendering tags causes close of XML stream.\n     * Therefore adding attributes must be done before rendering tags.\n     */\n\n    if (model.alignment) {\n      this.map.alignment.render(xmlStream, model.alignment);\n    }\n    if (model.protection) {\n      this.map.protection.render(xmlStream, model.protection);\n    }\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    // used during sax parsing of xml to build font object\n    switch (node.name) {\n      case 'xf':\n        this.model = {\n          numFmtId: parseInt(node.attributes.numFmtId, 10),\n          fontId: parseInt(node.attributes.fontId, 10),\n          fillId: parseInt(node.attributes.fillId, 10),\n          borderId: parseInt(node.attributes.borderId, 10),\n        };\n        if (this.xfId) {\n          this.model.xfId = parseInt(node.attributes.xfId, 10);\n        }\n        return true;\n      case 'alignment':\n        this.parser = this.map.alignment;\n        this.parser.parseOpen(node);\n        return true;\n      case 'protection':\n        this.parser = this.map.protection;\n        this.parser.parseOpen(node);\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        if (this.map.protection === this.parser) {\n          this.model.protection = this.parser.model;\n        } else {\n          this.model.alignment = this.parser.model;\n        }\n        this.parser = undefined;\n      }\n      return true;\n    }\n    return name !== 'xf';\n  }\n}\n\nmodule.exports = StyleXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AACnD,MAAME,eAAe,GAAGF,OAAO,CAAC,oBAAoB,CAAC;;AAErD;AACA;AACA;AACA;;AAEA;AACA,MAAMG,UAAU,SAASJ,SAAS,CAAC;EACjCK,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,IAAI,GAAG,CAAC,EAAED,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC;IACvC,IAAI,CAACC,GAAG,GAAG;MACTC,SAAS,EAAE,IAAIP,cAAc,CAAC,CAAC;MAC/BQ,UAAU,EAAE,IAAIP,eAAe,CAAC;IAClC,CAAC;EACH;EAEA,IAAIQ,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI;EACb;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,EAAE;MACvBC,QAAQ,EAAEF,KAAK,CAACE,QAAQ,IAAI,CAAC;MAC7BC,MAAM,EAAEH,KAAK,CAACG,MAAM,IAAI,CAAC;MACzBC,MAAM,EAAEJ,KAAK,CAACI,MAAM,IAAI,CAAC;MACzBC,QAAQ,EAAEL,KAAK,CAACK,QAAQ,IAAI;IAC9B,CAAC,CAAC;IACF,IAAI,IAAI,CAACZ,IAAI,EAAE;MACbM,SAAS,CAACO,YAAY,CAAC,MAAM,EAAEN,KAAK,CAACP,IAAI,IAAI,CAAC,CAAC;IACjD;IAEA,IAAIO,KAAK,CAACE,QAAQ,EAAE;MAClBH,SAAS,CAACO,YAAY,CAAC,mBAAmB,EAAE,GAAG,CAAC;IAClD;IACA,IAAIN,KAAK,CAACG,MAAM,EAAE;MAChBJ,SAAS,CAACO,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;IAC1C;IACA,IAAIN,KAAK,CAACI,MAAM,EAAE;MAChBL,SAAS,CAACO,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC;IAC1C;IACA,IAAIN,KAAK,CAACK,QAAQ,EAAE;MAClBN,SAAS,CAACO,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC;IAC5C;IACA,IAAIN,KAAK,CAACL,SAAS,EAAE;MACnBI,SAAS,CAACO,YAAY,CAAC,gBAAgB,EAAE,GAAG,CAAC;IAC/C;IACA,IAAIN,KAAK,CAACJ,UAAU,EAAE;MACpBG,SAAS,CAACO,YAAY,CAAC,iBAAiB,EAAE,GAAG,CAAC;IAChD;;IAEA;AACJ;AACA;AACA;;IAEI,IAAIN,KAAK,CAACL,SAAS,EAAE;MACnB,IAAI,CAACD,GAAG,CAACC,SAAS,CAACG,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACL,SAAS,CAAC;IACvD;IACA,IAAIK,KAAK,CAACJ,UAAU,EAAE;MACpB,IAAI,CAACF,GAAG,CAACE,UAAU,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACJ,UAAU,CAAC;IACzD;IAEAG,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI;QACP,IAAI,CAACX,KAAK,GAAG;UACXE,QAAQ,EAAEU,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACX,QAAQ,EAAE,EAAE,CAAC;UAChDC,MAAM,EAAES,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACV,MAAM,EAAE,EAAE,CAAC;UAC5CC,MAAM,EAAEQ,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACT,MAAM,EAAE,EAAE,CAAC;UAC5CC,QAAQ,EAAEO,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACR,QAAQ,EAAE,EAAE;QACjD,CAAC;QACD,IAAI,IAAI,CAACZ,IAAI,EAAE;UACb,IAAI,CAACO,KAAK,CAACP,IAAI,GAAGmB,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACpB,IAAI,EAAE,EAAE,CAAC;QACtD;QACA,OAAO,IAAI;MACb,KAAK,WAAW;QACd,IAAI,CAACiB,MAAM,GAAG,IAAI,CAAChB,GAAG,CAACC,SAAS;QAChC,IAAI,CAACe,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb,KAAK,YAAY;QACf,IAAI,CAACC,MAAM,GAAG,IAAI,CAAChB,GAAG,CAACE,UAAU;QACjC,IAAI,CAACc,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAK,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACL,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACI,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACL,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACM,UAAU,CAACL,IAAI,CAAC,EAAE;QACjC,IAAI,IAAI,CAACjB,GAAG,CAACE,UAAU,KAAK,IAAI,CAACc,MAAM,EAAE;UACvC,IAAI,CAACV,KAAK,CAACJ,UAAU,GAAG,IAAI,CAACc,MAAM,CAACV,KAAK;QAC3C,CAAC,MAAM;UACL,IAAI,CAACA,KAAK,CAACL,SAAS,GAAG,IAAI,CAACe,MAAM,CAACV,KAAK;QAC1C;QACA,IAAI,CAACU,MAAM,GAAGO,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,OAAON,IAAI,KAAK,IAAI;EACtB;AACF;AAEAO,MAAM,CAACC,OAAO,GAAG7B,UAAU"}