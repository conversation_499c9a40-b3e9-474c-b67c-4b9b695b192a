{"version": 3, "file": "phonetic-text-xform.js", "names": ["TextXform", "require", "RichTextXform", "BaseXform", "PhoneticTextXform", "constructor", "map", "r", "t", "tag", "render", "xmlStream", "model", "openNode", "sb", "eb", "hasOwnProperty", "richText", "for<PERSON>ach", "text", "closeNode", "parseOpen", "node", "name", "parser", "parseInt", "attributes", "parseText", "parseClose", "rt", "push", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/strings/phonetic-text-xform.js"], "sourcesContent": ["const TextXform = require('./text-xform');\nconst RichTextXform = require('./rich-text-xform');\n\nconst BaseXform = require('../base-xform');\n\n// <rPh sb=\"0\" eb=\"1\">\n//   <t>(its pronounciation in KATAKANA)</t>\n// </rPh>\n\nclass PhoneticTextXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      r: new RichTextXform(),\n      t: new TextXform(),\n    };\n  }\n\n  get tag() {\n    return 'rPh';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      sb: model.sb || 0,\n      eb: model.eb || 0,\n    });\n    if (model && model.hasOwnProperty('richText') && model.richText) {\n      const {r} = this.map;\n      model.richText.forEach(text => {\n        r.render(xmlStream, text);\n      });\n    } else if (model) {\n      this.map.t.render(xmlStream, model.text);\n    }\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    const {name} = node;\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    if (name === this.tag) {\n      this.model = {\n        sb: parseInt(node.attributes.sb, 10),\n        eb: parseInt(node.attributes.eb, 10),\n      };\n      return true;\n    }\n    this.parser = this.map[name];\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    return false;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        switch (name) {\n          case 'r': {\n            let rt = this.model.richText;\n            if (!rt) {\n              rt = this.model.richText = [];\n            }\n            rt.push(this.parser.model);\n            break;\n          }\n          case 't':\n            this.model.text = this.parser.model;\n            break;\n          default:\n            break;\n        }\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = PhoneticTextXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMC,aAAa,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAElD,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;;AAEA,MAAMG,iBAAiB,SAASD,SAAS,CAAC;EACxCE,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,CAAC,EAAE,IAAIL,aAAa,CAAC,CAAC;MACtBM,CAAC,EAAE,IAAIR,SAAS,CAAC;IACnB,CAAC;EACH;EAEA,IAAIS,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,EAAE,EAAEF,KAAK,CAACE,EAAE,IAAI,CAAC;MACjBC,EAAE,EAAEH,KAAK,CAACG,EAAE,IAAI;IAClB,CAAC,CAAC;IACF,IAAIH,KAAK,IAAIA,KAAK,CAACI,cAAc,CAAC,UAAU,CAAC,IAAIJ,KAAK,CAACK,QAAQ,EAAE;MAC/D,MAAM;QAACV;MAAC,CAAC,GAAG,IAAI,CAACD,GAAG;MACpBM,KAAK,CAACK,QAAQ,CAACC,OAAO,CAACC,IAAI,IAAI;QAC7BZ,CAAC,CAACG,MAAM,CAACC,SAAS,EAAEQ,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,KAAK,EAAE;MAChB,IAAI,CAACN,GAAG,CAACE,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACO,IAAI,CAAC;IAC1C;IACAR,SAAS,CAACS,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,MAAM;MAACC;IAAI,CAAC,GAAGD,IAAI;IACnB,IAAI,IAAI,CAACE,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACH,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,IAAIC,IAAI,KAAK,IAAI,CAACd,GAAG,EAAE;MACrB,IAAI,CAACG,KAAK,GAAG;QACXE,EAAE,EAAEW,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACZ,EAAE,EAAE,EAAE,CAAC;QACpCC,EAAE,EAAEU,QAAQ,CAACH,IAAI,CAACI,UAAU,CAACX,EAAE,EAAE,EAAE;MACrC,CAAC;MACD,OAAO,IAAI;IACb;IACA,IAAI,CAACS,MAAM,GAAG,IAAI,CAAClB,GAAG,CAACiB,IAAI,CAAC;IAC5B,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACH,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAK,SAASA,CAACR,IAAI,EAAE;IACd,IAAI,IAAI,CAACK,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACR,IAAI,CAAC;IAC7B;EACF;EAEAS,UAAUA,CAACL,IAAI,EAAE;IACf,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACL,IAAI,CAAC,EAAE;QACjC,QAAQA,IAAI;UACV,KAAK,GAAG;YAAE;cACR,IAAIM,EAAE,GAAG,IAAI,CAACjB,KAAK,CAACK,QAAQ;cAC5B,IAAI,CAACY,EAAE,EAAE;gBACPA,EAAE,GAAG,IAAI,CAACjB,KAAK,CAACK,QAAQ,GAAG,EAAE;cAC/B;cACAY,EAAE,CAACC,IAAI,CAAC,IAAI,CAACN,MAAM,CAACZ,KAAK,CAAC;cAC1B;YACF;UACA,KAAK,GAAG;YACN,IAAI,CAACA,KAAK,CAACO,IAAI,GAAG,IAAI,CAACK,MAAM,CAACZ,KAAK;YACnC;UACF;YACE;QACJ;QACA,IAAI,CAACY,MAAM,GAAGO,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQR,IAAI;MACV,KAAK,IAAI,CAACd,GAAG;QACX,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAuB,MAAM,CAACC,OAAO,GAAG7B,iBAAiB"}