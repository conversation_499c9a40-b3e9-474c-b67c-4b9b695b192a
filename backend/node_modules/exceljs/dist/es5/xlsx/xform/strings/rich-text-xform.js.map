{"version": 3, "file": "rich-text-xform.js", "names": ["TextXform", "require", "FontXform", "BaseXform", "RichTextXform", "constructor", "model", "tag", "textXform", "_textXform", "fontXform", "_fontXform", "FONT_OPTIONS", "render", "xmlStream", "openNode", "font", "text", "closeNode", "parseOpen", "node", "parser", "name", "parseText", "parseClose", "undefined", "tagName", "fontNameTag", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/strings/rich-text-xform.js"], "sourcesContent": ["const TextXform = require('./text-xform');\nconst FontXform = require('../style/font-xform');\n\nconst BaseXform = require('../base-xform');\n\n// <r>\n//   <rPr>\n//     <sz val=\"11\"/>\n//     <color theme=\"1\" tint=\"5\"/>\n//     <rFont val=\"Calibri\"/>\n//     <family val=\"2\"/>\n//     <scheme val=\"minor\"/>\n//   </rPr>\n//   <t xml:space=\"preserve\"> is </t>\n// </r>\n\nclass RichTextXform extends BaseXform {\n  constructor(model) {\n    super();\n\n    this.model = model;\n  }\n\n  get tag() {\n    return 'r';\n  }\n\n  get textXform() {\n    return this._textXform || (this._textXform = new TextXform());\n  }\n\n  get fontXform() {\n    return this._fontXform || (this._fontXform = new FontXform(RichTextXform.FONT_OPTIONS));\n  }\n\n  render(xmlStream, model) {\n    model = model || this.model;\n\n    xmlStream.openNode('r');\n    if (model.font) {\n      this.fontXform.render(xmlStream, model.font);\n    }\n    this.textXform.render(xmlStream, model.text);\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'r':\n        this.model = {};\n        return true;\n      case 't':\n        this.parser = this.textXform;\n        this.parser.parseOpen(node);\n        return true;\n      case 'rPr':\n        this.parser = this.fontXform;\n        this.parser.parseOpen(node);\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    switch (name) {\n      case 'r':\n        return false;\n      case 't':\n        this.model.text = this.parser.model;\n        this.parser = undefined;\n        return true;\n      case 'rPr':\n        this.model.font = this.parser.model;\n        this.parser = undefined;\n        return true;\n      default:\n        if (this.parser) {\n          this.parser.parseClose(name);\n        }\n        return true;\n    }\n  }\n}\n\nRichTextXform.FONT_OPTIONS = {\n  tagName: 'rPr',\n  fontNameTag: 'rFont',\n};\n\nmodule.exports = RichTextXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMC,SAAS,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAEhD,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMG,aAAa,SAASD,SAAS,CAAC;EACpCE,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,GAAG;EACZ;EAEA,IAAIC,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,UAAU,KAAK,IAAI,CAACA,UAAU,GAAG,IAAIT,SAAS,CAAC,CAAC,CAAC;EAC/D;EAEA,IAAIU,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,UAAU,KAAK,IAAI,CAACA,UAAU,GAAG,IAAIT,SAAS,CAACE,aAAa,CAACQ,YAAY,CAAC,CAAC;EACzF;EAEAC,MAAMA,CAACC,SAAS,EAAER,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACA,KAAK;IAE3BQ,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC;IACvB,IAAIT,KAAK,CAACU,IAAI,EAAE;MACd,IAAI,CAACN,SAAS,CAACG,MAAM,CAACC,SAAS,EAAER,KAAK,CAACU,IAAI,CAAC;IAC9C;IACA,IAAI,CAACR,SAAS,CAACK,MAAM,CAACC,SAAS,EAAER,KAAK,CAACW,IAAI,CAAC;IAC5CH,SAAS,CAACI,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,GAAG;QACN,IAAI,CAAChB,KAAK,GAAG,CAAC,CAAC;QACf,OAAO,IAAI;MACb,KAAK,GAAG;QACN,IAAI,CAACe,MAAM,GAAG,IAAI,CAACb,SAAS;QAC5B,IAAI,CAACa,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb,KAAK,KAAK;QACR,IAAI,CAACC,MAAM,GAAG,IAAI,CAACX,SAAS;QAC5B,IAAI,CAACW,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAG,SAASA,CAACN,IAAI,EAAE;IACd,IAAI,IAAI,CAACI,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACE,SAAS,CAACN,IAAI,CAAC;IAC7B;EACF;EAEAO,UAAUA,CAACF,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,GAAG;QACN,OAAO,KAAK;MACd,KAAK,GAAG;QACN,IAAI,CAAChB,KAAK,CAACW,IAAI,GAAG,IAAI,CAACI,MAAM,CAACf,KAAK;QACnC,IAAI,CAACe,MAAM,GAAGI,SAAS;QACvB,OAAO,IAAI;MACb,KAAK,KAAK;QACR,IAAI,CAACnB,KAAK,CAACU,IAAI,GAAG,IAAI,CAACK,MAAM,CAACf,KAAK;QACnC,IAAI,CAACe,MAAM,GAAGI,SAAS;QACvB,OAAO,IAAI;MACb;QACE,IAAI,IAAI,CAACJ,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACG,UAAU,CAACF,IAAI,CAAC;QAC9B;QACA,OAAO,IAAI;IACf;EACF;AACF;AAEAlB,aAAa,CAACQ,YAAY,GAAG;EAC3Bc,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE;AACf,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGzB,aAAa"}