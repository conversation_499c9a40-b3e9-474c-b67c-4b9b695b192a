import type _ts from 'typescript';
import type { TsCompilerInstance } from '../types';
/**
 * Remember to increase the version whenever transformer's content is changed. This is to inform <PERSON><PERSON> to not reuse
 * the previous cache which contains old transformer's content
 */
export declare const version = 4;
export declare const name = "hoist-jest";
export declare function factory({ configSet }: TsCompilerInstance): (ctx: _ts.TransformationContext) => _ts.Transformer<_ts.SourceFile>;
