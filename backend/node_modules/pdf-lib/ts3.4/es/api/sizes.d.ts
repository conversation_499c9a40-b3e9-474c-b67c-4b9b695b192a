export declare const PageSizes: {
    '4A0': [number, number];
    '2A0': [number, number];
    A0: [number, number];
    A1: [number, number];
    A2: [number, number];
    A3: [number, number];
    A4: [number, number];
    A5: [number, number];
    A6: [number, number];
    A7: [number, number];
    A8: [number, number];
    A9: [number, number];
    A10: [number, number];
    B0: [number, number];
    B1: [number, number];
    B2: [number, number];
    B3: [number, number];
    B4: [number, number];
    B5: [number, number];
    B6: [number, number];
    B7: [number, number];
    B8: [number, number];
    B9: [number, number];
    B10: [number, number];
    C0: [number, number];
    C1: [number, number];
    C2: [number, number];
    C3: [number, number];
    C4: [number, number];
    C5: [number, number];
    C6: [number, number];
    C7: [number, number];
    C8: [number, number];
    C9: [number, number];
    C10: [number, number];
    RA0: [number, number];
    RA1: [number, number];
    RA2: [number, number];
    RA3: [number, number];
    RA4: [number, number];
    SRA0: [number, number];
    SRA1: [number, number];
    SRA2: [number, number];
    SRA3: [number, number];
    SRA4: [number, number];
    Executive: [number, number];
    Folio: [number, number];
    Legal: [number, number];
    Letter: [number, number];
    Tabloid: [number, number];
};
//# sourceMappingURL=sizes.d.ts.map
