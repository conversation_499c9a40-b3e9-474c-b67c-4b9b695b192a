{"name": "@napi-rs/canvas-linux-x64-gnu", "version": "0.1.71", "os": ["linux"], "cpu": ["x64"], "main": "skia.linux-x64-gnu.node", "files": ["skia.linux-x64-gnu.node"], "description": "Canvas for Node.js with skia backend", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api", "canvas", "image", "pdf", "svg", "skia"], "license": "MIT", "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Brooooooklyn/canvas.git"}, "libc": ["glibc"]}