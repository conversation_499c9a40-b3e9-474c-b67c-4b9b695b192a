# Installation
> `npm install --save @types/superagent`

# Summary
This package contains type definitions for superagent (https://github.com/visionmedia/superagent).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/superagent.

### Additional Details
 * Last updated: Mon, 26 Aug 2024 02:45:06 GMT
 * Dependencies: [@types/cookiejar](https://npmjs.com/package/@types/cookiejar), [@types/methods](https://npmjs.com/package/@types/methods), [@types/node](https://npmjs.com/package/@types/node), [form-data](https://npmjs.com/package/form-data)

# Credits
These definitions were written by [<PERSON>](https://github.com/NicoZelaya), [<PERSON>](https://github.com/mxl), [<PERSON><PERSON><PERSON>](https://github.com/shreyjain1994), [<PERSON>](https://github.com/zopf), [<PERSON>](https://github.com/beeequeue), [<PERSON><PERSON>](https://github.com/lukaselmer), [<PERSON>](https://github.com/theQuazz), [<PERSON>](https://github.com/carnesen), [<PERSON>berg](https://github.com/ghostganz), [LuckyWind_sck](https://github.com/LuckyWindsck), and [David Tanner](https://github.com/DavidTanner).
