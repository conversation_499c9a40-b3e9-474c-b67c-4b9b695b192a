declare class Agent {
    use(...args: any[]): this;
    on(...args: any[]): this;
    once(...args: any[]): this;
    set(...args: any[]): this;
    query(...args: any[]): this;
    type(...args: any[]): this;
    accept(...args: any[]): this;
    auth(...args: any[]): this;
    withCredentials(...args: any[]): this;
    sortQuery(...args: any[]): this;
    retry(...args: any[]): this;
    ok(...args: any[]): this;
    redirects(...args: any[]): this;
    timeout(...args: any[]): this;
    buffer(...args: any[]): this;
    serialize(...args: any[]): this;
    parse(...args: any[]): this;
    ca(...args: any[]): this;
    key(...args: any[]): this;
    pfx(...args: any[]): this;
    cert(...args: any[]): this;
    disableTLSCerts(...args: any[]): this;
}

export = Agent;
