import type { Knex } from "knex";

<% if (d.tableName) { %>
export async function up(knex: Knex): Promise<Knex.SchemaBuilder> {
    return knex.schema.createTable("<%= d.tableName %>", (t) => {
        t.increments();
        t.timestamps();
    });
}
<% } else { %>
export async function up(knex: Knex): Promise<void> {
}
<% } %>
<% if (d.tableName) { %>
export async function down(knex: Knex): Promise<Knex.SchemaBuilder> {
    return knex.schema.dropTable("<%= d.tableName %>");
}
<% } else { %>
export async function down(knex: Knex): Promise<void> {
}
<% } %>
