import { PDFDocument, rgb } from 'pdf-lib';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/logger';
import { ExcelGeneratorService } from './ExcelGeneratorService';
import { ComplexityAnalyzer } from './ComplexityAnalyzer';
import { TextExtractionService } from './TextExtractionService';
import { OCRService } from './OCRService';
import { CacheService } from './CacheService';

export interface ProcessingOptions {
  template?: string;
  outputFormat?: 'xlsx' | 'csv';
  extractTables?: boolean;
  extractText?: boolean;
  ocrEnabled?: boolean;
}

export interface ProcessingResult {
  success: boolean;
  outputPath?: string;
  extractedData?: any;
  processingTime: number;
  complexity: 'simple' | 'medium' | 'complex';
  method: 'local' | 'cloud' | 'hybrid';
  error?: string;
}

export interface ProcessingProgress {
  stage: 'analyzing' | 'extracting' | 'processing' | 'generating' | 'completed' | 'failed';
  progress: number;
  message: string;
}

export class PDFProcessingService {
  private excelGenerator: ExcelGeneratorService;
  private complexityAnalyzer: ComplexityAnalyzer;
  private textExtractor: TextExtractionService;
  private ocrService: OCRService;
  private cacheService: CacheService;

  constructor() {
    this.excelGenerator = new ExcelGeneratorService();
    this.complexityAnalyzer = new ComplexityAnalyzer();
    this.textExtractor = new TextExtractionService();
    this.ocrService = new OCRService();
    this.cacheService = new CacheService();
  }

  /**
   * Main processing method - orchestrates the entire PDF to Excel conversion
   */
  async processPDF(
    filePath: string,
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      logger.info(`Starting PDF processing: ${filePath}`);

      // Check cache first
      const cachedResult = await this.cacheService.getCachedProcessingResult(filePath, options);
      if (cachedResult) {
        logger.info('Using cached processing result');

        progressCallback?.({
          stage: 'completed',
          progress: 100,
          message: 'Using cached result - processing completed instantly!'
        });

        return {
          success: true,
          outputPath: await this.regenerateExcelFromCache(cachedResult, filePath, options),
          extractedData: cachedResult.extractedData,
          processingTime: 100, // Minimal time for cache retrieval
          complexity: cachedResult.complexity?.level || 'simple',
          method: 'local'
        };
      }

      // Stage 1: Load and analyze PDF complexity
      progressCallback?.({
        stage: 'analyzing',
        progress: 10,
        message: 'Analyzing document complexity...'
      });

      const pdfBuffer = await fs.readFile(filePath);
      const pdfDoc = await PDFDocument.load(pdfBuffer);

      // Check for cached complexity analysis
      let complexity = await this.cacheService.getCachedComplexityAnalysis(filePath);
      if (!complexity) {
        complexity = await this.complexityAnalyzer.analyze(pdfDoc, pdfBuffer);
        await this.cacheService.cacheComplexityAnalysis(filePath, complexity);
      }

      logger.info(`Document complexity: ${complexity.level}`);

      // Stage 2: Choose processing strategy based on complexity
      const processingMethod = this.selectProcessingMethod(complexity);
      
      progressCallback?.({
        stage: 'extracting',
        progress: 30,
        message: `Extracting data using ${processingMethod} processing...`
      });

      // Stage 3: Check if OCR is needed for scanned documents
      let extractedData: any;
      const isScanned = await OCRService.isScannedDocument(filePath);
      if (isScanned && options.ocrEnabled) {
        progressCallback?.({
          stage: 'extracting',
          progress: 35,
          message: 'Scanned document detected - using OCR processing...'
        });

        extractedData = await this.processWithOCR(filePath, options, progressCallback);
      } else {
        // Stage 3: Extract data based on chosen method
        if (processingMethod === 'local') {
          extractedData = await this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
        } else if (processingMethod === 'cloud') {
          extractedData = await this.processWithCloud(pdfBuffer, options, progressCallback);
        } else {
          extractedData = await this.processHybrid(pdfDoc, pdfBuffer, options, progressCallback);
        }
      }

      // Stage 4: Generate Excel file
      progressCallback?.({
        stage: 'generating',
        progress: 80,
        message: 'Generating Excel file...'
      });

      const outputPath = await this.excelGenerator.generateExcel(
        extractedData, 
        filePath, 
        options
      );

      progressCallback?.({
        stage: 'completed',
        progress: 100,
        message: 'Processing completed successfully!'
      });

      const processingTime = Date.now() - startTime;

      const result = {
        success: true,
        outputPath,
        extractedData,
        processingTime,
        complexity: complexity.level,
        method: processingMethod
      };

      // Cache the successful result
      await this.cacheService.cacheProcessingResult(filePath, options, result);

      return result;

    } catch (error) {
      logger.error('PDF processing failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      progressCallback?.({
        stage: 'failed',
        progress: 0,
        message: `Processing failed: ${errorMessage}`
      });

      return {
        success: false,
        processingTime: Date.now() - startTime,
        complexity: 'simple',
        method: 'local',
        error: errorMessage
      };
    }
  }

  /**
   * Select processing method based on document complexity
   */
  private selectProcessingMethod(complexity: any): 'local' | 'cloud' | 'hybrid' {
    // For Phase 3, we'll start with local processing
    // Later phases will add cloud fallback for complex documents
    
    if (complexity.level === 'simple') {
      return 'local';
    } else if (complexity.level === 'medium') {
      return 'local'; // Will be 'hybrid' in later phases
    } else {
      return 'local'; // Will be 'cloud' in later phases
    }
  }

  /**
   * Local AI processing using lightweight models
   */
  private async processLocally(
    pdfDoc: PDFDocument, 
    pdfBuffer: Buffer, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {
    
    progressCallback?.({
      stage: 'processing',
      progress: 40,
      message: 'Extracting text content...'
    });

    // Extract text using pdf-lib and custom text extraction
    const textData = await this.textExtractor.extractText(pdfDoc, pdfBuffer);
    
    progressCallback?.({
      stage: 'processing',
      progress: 60,
      message: 'Detecting tables and structure...'
    });

    // Detect tables and structured data
    const structuredData = await this.textExtractor.detectTables(textData);
    
    progressCallback?.({
      stage: 'processing',
      progress: 70,
      message: 'Processing extracted data...'
    });

    // Process and clean the data
    const processedData = this.processExtractedData(structuredData, options);

    return processedData;
  }

  /**
   * Cloud processing for complex documents (placeholder for future implementation)
   */
  private async processWithCloud(
    pdfBuffer: Buffer, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {
    // Placeholder for cloud AI processing
    // Will be implemented in Phase 4
    throw new Error('Cloud processing not yet implemented');
  }

  /**
   * Hybrid processing combining local and cloud methods
   */
  private async processHybrid(
    pdfDoc: PDFDocument, 
    pdfBuffer: Buffer, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {
    // Placeholder for hybrid processing
    // Will be implemented in Phase 4
    return this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
  }

  /**
   * Process and clean extracted data
   */
  private processExtractedData(rawData: any, options: ProcessingOptions): any {
    // Clean and structure the extracted data
    const processedData = {
      tables: [],
      text: [],
      metadata: {
        extractionMethod: 'local',
        timestamp: new Date().toISOString(),
        options
      }
    };

    // Process tables if detected
    if (rawData.tables && rawData.tables.length > 0) {
      processedData.tables = rawData.tables.map((table: any) => ({
        headers: table.headers || [],
        rows: table.rows || [],
        position: table.position || { page: 1, x: 0, y: 0 }
      }));
    }

    // Process text content
    if (rawData.text) {
      processedData.text = Array.isArray(rawData.text) ? rawData.text : [rawData.text];
    }

    return processedData;
  }

  /**
   * Process PDF with OCR for scanned documents
   */
  private async processWithOCR(
    filePath: string,
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {

    try {
      progressCallback?.({
        stage: 'processing',
        progress: 40,
        message: 'Running OCR on scanned document...'
      });

      // Process with OCR
      const ocrResults = await this.ocrService.processPDFWithOCR(filePath, {
        language: 'eng+pol',
        preprocessImage: true,
        dpi: 300
      });

      progressCallback?.({
        stage: 'processing',
        progress: 70,
        message: 'Processing OCR results...'
      });

      // Convert OCR results to our standard format
      const processedData: any = {
        tables: [],
        text: [],
        metadata: {
          extractionMethod: 'ocr',
          timestamp: new Date().toISOString(),
          options,
          ocrResults: ocrResults.length
        }
      };

      // Process OCR text results
      ocrResults.forEach(pageResult => {
        // Add text content
        if (pageResult.result.text) {
          processedData.text.push({
            content: pageResult.result.text,
            page: pageResult.pageNumber,
            position: { x: 0, y: 0, width: 0, height: 0 },
            fontSize: 12,
            fontName: 'ocr-extracted'
          });
        }

        // Try to detect tables in OCR text
        const lines = pageResult.result.lines;
        if (lines && lines.length > 0) {
          const tableCandidate = this.detectTablesInOCRText(lines, pageResult.pageNumber);
          if (tableCandidate) {
            processedData.tables.push(tableCandidate);
          }
        }
      });

      return processedData;

    } catch (error) {
      logger.error('OCR processing failed:', error);
      throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Detect tables in OCR text
   */
  private detectTablesInOCRText(lines: any[], pageNumber: number): any | null {
    // Simple table detection in OCR text
    // Look for lines with multiple words that could be columns

    const potentialTableLines = lines.filter(line => {
      const words = line.text.trim().split(/\s+/);
      return words.length >= 2 && words.length <= 10; // Reasonable column count
    });

    if (potentialTableLines.length < 2) {
      return null;
    }

    // Extract headers and rows
    const headers = potentialTableLines[0].text.trim().split(/\s+/);
    const rows = potentialTableLines.slice(1).map(line =>
      line.text.trim().split(/\s+/)
    );

    return {
      headers,
      rows,
      position: {
        page: pageNumber,
        x: 0,
        y: 0
      },
      confidence: 0.7 // Lower confidence for OCR-detected tables
    };
  }

  /**
   * Regenerate Excel from cached data
   */
  private async regenerateExcelFromCache(
    cachedResult: any,
    originalFilePath: string,
    options: ProcessingOptions
  ): Promise<string> {

    try {
      // Generate new Excel file from cached data
      const outputPath = await this.excelGenerator.generateExcel(
        cachedResult.extractedData,
        originalFilePath,
        options
      );

      return outputPath;

    } catch (error) {
      logger.error('Failed to regenerate Excel from cache:', error);
      throw error;
    }
  }

  /**
   * Get supported file formats
   */
  static getSupportedFormats(): string[] {
    return ['pdf'];
  }

  /**
   * Validate PDF file
   */
  static async validatePDF(filePath: string): Promise<boolean> {
    try {
      const buffer = await fs.readFile(filePath);
      await PDFDocument.load(buffer);
      return true;
    } catch (error) {
      logger.error('PDF validation failed:', error);
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    try {
      await this.ocrService.cleanup();
      await this.cacheService.disconnect();
    } catch (error) {
      logger.error('Cleanup failed:', error);
    }
  }
}
