import { PDFDocument, rgb } from 'pdf-lib';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/logger';
import { ExcelGeneratorService } from './ExcelGeneratorService';
import { ComplexityAnalyzer } from './ComplexityAnalyzer';
import { TextExtractionService } from './TextExtractionService';

export interface ProcessingOptions {
  template?: string;
  outputFormat?: 'xlsx' | 'csv';
  extractTables?: boolean;
  extractText?: boolean;
  ocrEnabled?: boolean;
}

export interface ProcessingResult {
  success: boolean;
  outputPath?: string;
  extractedData?: any;
  processingTime: number;
  complexity: 'simple' | 'medium' | 'complex';
  method: 'local' | 'cloud' | 'hybrid';
  error?: string;
}

export interface ProcessingProgress {
  stage: 'analyzing' | 'extracting' | 'processing' | 'generating' | 'completed' | 'failed';
  progress: number;
  message: string;
}

export class PDFProcessingService {
  private excelGenerator: ExcelGeneratorService;
  private complexityAnalyzer: ComplexityAnalyzer;
  private textExtractor: TextExtractionService;

  constructor() {
    this.excelGenerator = new ExcelGeneratorService();
    this.complexityAnalyzer = new ComplexityAnalyzer();
    this.textExtractor = new TextExtractionService();
  }

  /**
   * Main processing method - orchestrates the entire PDF to Excel conversion
   */
  async processPDF(
    filePath: string, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<ProcessingResult> {
    const startTime = Date.now();
    
    try {
      logger.info(`Starting PDF processing: ${filePath}`);
      
      // Stage 1: Load and analyze PDF complexity
      progressCallback?.({
        stage: 'analyzing',
        progress: 10,
        message: 'Analyzing document complexity...'
      });

      const pdfBuffer = await fs.readFile(filePath);
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      
      const complexity = await this.complexityAnalyzer.analyze(pdfDoc, pdfBuffer);
      logger.info(`Document complexity: ${complexity.level}`);

      // Stage 2: Choose processing strategy based on complexity
      const processingMethod = this.selectProcessingMethod(complexity);
      
      progressCallback?.({
        stage: 'extracting',
        progress: 30,
        message: `Extracting data using ${processingMethod} processing...`
      });

      // Stage 3: Extract data based on chosen method
      let extractedData;
      if (processingMethod === 'local') {
        extractedData = await this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
      } else if (processingMethod === 'cloud') {
        extractedData = await this.processWithCloud(pdfBuffer, options, progressCallback);
      } else {
        extractedData = await this.processHybrid(pdfDoc, pdfBuffer, options, progressCallback);
      }

      // Stage 4: Generate Excel file
      progressCallback?.({
        stage: 'generating',
        progress: 80,
        message: 'Generating Excel file...'
      });

      const outputPath = await this.excelGenerator.generateExcel(
        extractedData, 
        filePath, 
        options
      );

      progressCallback?.({
        stage: 'completed',
        progress: 100,
        message: 'Processing completed successfully!'
      });

      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        outputPath,
        extractedData,
        processingTime,
        complexity: complexity.level,
        method: processingMethod
      };

    } catch (error) {
      logger.error('PDF processing failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      progressCallback?.({
        stage: 'failed',
        progress: 0,
        message: `Processing failed: ${errorMessage}`
      });

      return {
        success: false,
        processingTime: Date.now() - startTime,
        complexity: 'simple',
        method: 'local',
        error: errorMessage
      };
    }
  }

  /**
   * Select processing method based on document complexity
   */
  private selectProcessingMethod(complexity: any): 'local' | 'cloud' | 'hybrid' {
    // For Phase 3, we'll start with local processing
    // Later phases will add cloud fallback for complex documents
    
    if (complexity.level === 'simple') {
      return 'local';
    } else if (complexity.level === 'medium') {
      return 'local'; // Will be 'hybrid' in later phases
    } else {
      return 'local'; // Will be 'cloud' in later phases
    }
  }

  /**
   * Local AI processing using lightweight models
   */
  private async processLocally(
    pdfDoc: PDFDocument, 
    pdfBuffer: Buffer, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {
    
    progressCallback?.({
      stage: 'processing',
      progress: 40,
      message: 'Extracting text content...'
    });

    // Extract text using pdf-lib and custom text extraction
    const textData = await this.textExtractor.extractText(pdfDoc, pdfBuffer);
    
    progressCallback?.({
      stage: 'processing',
      progress: 60,
      message: 'Detecting tables and structure...'
    });

    // Detect tables and structured data
    const structuredData = await this.textExtractor.detectTables(textData);
    
    progressCallback?.({
      stage: 'processing',
      progress: 70,
      message: 'Processing extracted data...'
    });

    // Process and clean the data
    const processedData = this.processExtractedData(structuredData, options);

    return processedData;
  }

  /**
   * Cloud processing for complex documents (placeholder for future implementation)
   */
  private async processWithCloud(
    pdfBuffer: Buffer, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {
    // Placeholder for cloud AI processing
    // Will be implemented in Phase 4
    throw new Error('Cloud processing not yet implemented');
  }

  /**
   * Hybrid processing combining local and cloud methods
   */
  private async processHybrid(
    pdfDoc: PDFDocument, 
    pdfBuffer: Buffer, 
    options: ProcessingOptions,
    progressCallback?: (progress: ProcessingProgress) => void
  ): Promise<any> {
    // Placeholder for hybrid processing
    // Will be implemented in Phase 4
    return this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
  }

  /**
   * Process and clean extracted data
   */
  private processExtractedData(rawData: any, options: ProcessingOptions): any {
    // Clean and structure the extracted data
    const processedData = {
      tables: [],
      text: [],
      metadata: {
        extractionMethod: 'local',
        timestamp: new Date().toISOString(),
        options
      }
    };

    // Process tables if detected
    if (rawData.tables && rawData.tables.length > 0) {
      processedData.tables = rawData.tables.map((table: any) => ({
        headers: table.headers || [],
        rows: table.rows || [],
        position: table.position || { page: 1, x: 0, y: 0 }
      }));
    }

    // Process text content
    if (rawData.text) {
      processedData.text = Array.isArray(rawData.text) ? rawData.text : [rawData.text];
    }

    return processedData;
  }

  /**
   * Get supported file formats
   */
  static getSupportedFormats(): string[] {
    return ['pdf'];
  }

  /**
   * Validate PDF file
   */
  static async validatePDF(filePath: string): Promise<boolean> {
    try {
      const buffer = await fs.readFile(filePath);
      await PDFDocument.load(buffer);
      return true;
    } catch (error) {
      logger.error('PDF validation failed:', error);
      return false;
    }
  }
}
