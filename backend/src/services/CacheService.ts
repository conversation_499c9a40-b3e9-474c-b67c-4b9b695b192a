import * as Redis from 'redis';
import * as crypto from 'crypto';
import { logger } from '../utils/logger';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean;
  namespace?: string;
}

export interface ProcessingCache {
  fileHash: string;
  complexity: any;
  extractedData: any;
  processingTime: number;
  timestamp: number;
  version: string;
}

export class CacheService {
  private client: any | null = null;
  private isConnected = false;
  private readonly defaultTTL = 3600; // 1 hour
  private readonly namespace = 'pdf-processing';

  constructor() {
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection (simplified - no Redis for now)
   */
  private async initializeRedis(): Promise<void> {
    try {
      // For now, we'll use in-memory cache instead of Redis
      logger.info('Using in-memory cache (Redis disabled)');
      this.isConnected = false; // Keep Redis disabled for now
    } catch (error) {
      logger.error('Failed to initialize cache:', error);
      this.isConnected = false;
    }
  }

  /**
   * Generate cache key for file
   */
  private generateFileHash(filePath: string, options: any = {}): string {
    const fs = require('fs');
    
    try {
      // Create hash based on file content and processing options
      const fileBuffer = fs.readFileSync(filePath);
      const optionsString = JSON.stringify(options);
      
      return crypto
        .createHash('sha256')
        .update(fileBuffer)
        .update(optionsString)
        .digest('hex');
        
    } catch (error) {
      logger.error('Failed to generate file hash:', error);
      // Fallback to filename + timestamp
      return crypto
        .createHash('sha256')
        .update(filePath + Date.now().toString())
        .digest('hex');
    }
  }

  /**
   * Generate cache key
   */
  private getCacheKey(type: string, identifier: string, namespace?: string): string {
    const ns = namespace || this.namespace;
    return `${ns}:${type}:${identifier}`;
  }

  /**
   * Cache processing result
   */
  async cacheProcessingResult(
    filePath: string,
    options: any,
    result: any,
    cacheOptions: CacheOptions = {}
  ): Promise<boolean> {
    
    if (!this.isConnected || !this.client) {
      logger.warn('Redis not connected, skipping cache');
      return false;
    }

    try {
      const fileHash = this.generateFileHash(filePath, options);
      const cacheKey = this.getCacheKey('processing', fileHash, cacheOptions.namespace);
      
      const cacheData: ProcessingCache = {
        fileHash,
        complexity: result.complexity,
        extractedData: result.extractedData,
        processingTime: result.processingTime,
        timestamp: Date.now(),
        version: '1.0'
      };

      const ttl = cacheOptions.ttl || this.defaultTTL;
      
      await this.client.setEx(
        cacheKey,
        ttl,
        JSON.stringify(cacheData)
      );

      logger.info(`Cached processing result for file hash: ${fileHash}`);
      return true;

    } catch (error) {
      logger.error('Failed to cache processing result:', error);
      return false;
    }
  }

  /**
   * Get cached processing result
   */
  async getCachedProcessingResult(
    filePath: string,
    options: any,
    namespace?: string
  ): Promise<ProcessingCache | null> {
    
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const fileHash = this.generateFileHash(filePath, options);
      const cacheKey = this.getCacheKey('processing', fileHash, namespace);
      
      const cachedData = await this.client.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      const parsed = JSON.parse(cachedData) as ProcessingCache;
      
      // Check if cache is still valid (additional validation)
      const age = Date.now() - parsed.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (age > maxAge) {
        // Cache is too old, remove it
        await this.client.del(cacheKey);
        return null;
      }

      logger.info(`Cache hit for file hash: ${fileHash}`);
      return parsed;

    } catch (error) {
      logger.error('Failed to get cached result:', error);
      return null;
    }
  }

  /**
   * Cache complexity analysis
   */
  async cacheComplexityAnalysis(
    filePath: string,
    complexity: any,
    cacheOptions: CacheOptions = {}
  ): Promise<boolean> {
    
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const fileHash = this.generateFileHash(filePath);
      const cacheKey = this.getCacheKey('complexity', fileHash, cacheOptions.namespace);
      
      const cacheData = {
        fileHash,
        complexity,
        timestamp: Date.now()
      };

      const ttl = cacheOptions.ttl || this.defaultTTL * 2; // Longer TTL for complexity
      
      await this.client.setEx(
        cacheKey,
        ttl,
        JSON.stringify(cacheData)
      );

      return true;

    } catch (error) {
      logger.error('Failed to cache complexity analysis:', error);
      return false;
    }
  }

  /**
   * Get cached complexity analysis
   */
  async getCachedComplexityAnalysis(
    filePath: string,
    namespace?: string
  ): Promise<any | null> {
    
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const fileHash = this.generateFileHash(filePath);
      const cacheKey = this.getCacheKey('complexity', fileHash, namespace);
      
      const cachedData = await this.client.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      const parsed = JSON.parse(cachedData);
      return parsed.complexity;

    } catch (error) {
      logger.error('Failed to get cached complexity:', error);
      return null;
    }
  }

  /**
   * Cache user session data
   */
  async cacheUserSession(
    userId: string,
    sessionData: any,
    ttl: number = 3600
  ): Promise<boolean> {
    
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      const cacheKey = this.getCacheKey('session', userId);
      
      await this.client.setEx(
        cacheKey,
        ttl,
        JSON.stringify(sessionData)
      );

      return true;

    } catch (error) {
      logger.error('Failed to cache user session:', error);
      return false;
    }
  }

  /**
   * Get cached user session
   */
  async getCachedUserSession(userId: string): Promise<any | null> {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const cacheKey = this.getCacheKey('session', userId);
      const cachedData = await this.client.get(cacheKey);
      
      return cachedData ? JSON.parse(cachedData) : null;

    } catch (error) {
      logger.error('Failed to get cached user session:', error);
      return null;
    }
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidateCache(pattern: string, namespace?: string): Promise<number> {
    if (!this.isConnected || !this.client) {
      return 0;
    }

    try {
      const ns = namespace || this.namespace;
      const searchPattern = `${ns}:${pattern}`;
      
      const keys = await this.client.keys(searchPattern);
      
      if (keys.length === 0) {
        return 0;
      }

      const deleted = await this.client.del(keys);
      logger.info(`Invalidated ${deleted} cache entries matching pattern: ${searchPattern}`);
      
      return deleted;

    } catch (error) {
      logger.error('Failed to invalidate cache:', error);
      return 0;
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<any> {
    if (!this.isConnected || !this.client) {
      return null;
    }

    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');
      
      return {
        connected: this.isConnected,
        memory: info,
        keyspace: keyspace,
        namespace: this.namespace
      };

    } catch (error) {
      logger.error('Failed to get cache stats:', error);
      return null;
    }
  }

  /**
   * Clear all cache
   */
  async clearAllCache(): Promise<boolean> {
    if (!this.isConnected || !this.client) {
      return false;
    }

    try {
      await this.client.flushAll();
      logger.info('All cache cleared');
      return true;

    } catch (error) {
      logger.error('Failed to clear cache:', error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        await this.client.quit();
        this.isConnected = false;
        logger.info('Redis client disconnected');
      } catch (error) {
        logger.error('Failed to disconnect Redis client:', error);
      }
    }
  }
}
