import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs/promises';
import * as path from 'path';
import { db } from '../config/database';
import { logger } from '../utils/logger';
import { PDFProcessingService, ProcessingOptions } from './PDFProcessingService';
import { CustomError } from '../middleware/errorHandler';

export interface BatchJob {
  id: string;
  userId: string;
  name: string;
  files: BatchFile[];
  options: ProcessingOptions;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: {
    total: number;
    completed: number;
    failed: number;
    currentFile?: string;
  };
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  results?: BatchResult[];
  error?: string;
}

export interface BatchFile {
  id: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processingTime?: number;
  outputPath?: string;
  error?: string;
}

export interface BatchResult {
  fileId: string;
  fileName: string;
  success: boolean;
  outputPath?: string;
  processingTime: number;
  complexity?: string;
  error?: string;
}

export interface BatchOptions {
  maxConcurrent?: number;
  priority?: 'low' | 'normal' | 'high';
  notifyOnComplete?: boolean;
  retryFailures?: boolean;
  maxRetries?: number;
}

export class BatchProcessingService {
  private pdfProcessor: PDFProcessingService;
  private activeJobs: Map<string, BatchJob> = new Map();
  private readonly maxConcurrentJobs = 3;
  private readonly maxConcurrentFiles = 2;

  constructor() {
    this.pdfProcessor = new PDFProcessingService();
  }

  /**
   * Create a new batch job
   */
  async createBatchJob(
    userId: string,
    files: Express.Multer.File[],
    options: ProcessingOptions,
    batchOptions: BatchOptions = {}
  ): Promise<BatchJob> {
    
    try {
      const jobId = uuidv4();
      
      // Validate files
      if (!files || files.length === 0) {
        throw new CustomError('No files provided for batch processing', 400, true, 'NO_FILES');
      }

      if (files.length > 50) {
        throw new CustomError('Maximum 50 files allowed per batch', 400, true, 'TOO_MANY_FILES');
      }

      // Create batch files
      const batchFiles: BatchFile[] = files.map(file => ({
        id: uuidv4(),
        originalName: file.originalname,
        filePath: file.path,
        fileSize: file.size,
        status: 'pending'
      }));

      // Create batch job
      const batchJob: BatchJob = {
        id: jobId,
        userId,
        name: `Batch Job ${new Date().toISOString().slice(0, 19)}`,
        files: batchFiles,
        options,
        status: 'pending',
        progress: {
          total: batchFiles.length,
          completed: 0,
          failed: 0
        },
        createdAt: new Date()
      };

      // Save to database
      await this.saveBatchJobToDatabase(batchJob);

      // Add to active jobs
      this.activeJobs.set(jobId, batchJob);

      logger.info(`Batch job created: ${jobId} with ${files.length} files`);

      // Start processing asynchronously
      this.processBatchJobAsync(jobId, batchOptions);

      return batchJob;

    } catch (error) {
      logger.error('Failed to create batch job:', error);
      throw error;
    }
  }

  /**
   * Get batch job status
   */
  async getBatchJob(jobId: string, userId: string): Promise<BatchJob | null> {
    try {
      // Check active jobs first
      const activeJob = this.activeJobs.get(jobId);
      if (activeJob && activeJob.userId === userId) {
        return activeJob;
      }

      // Check database
      const job = await this.getBatchJobFromDatabase(jobId, userId);
      return job;

    } catch (error) {
      logger.error('Failed to get batch job:', error);
      return null;
    }
  }

  /**
   * Get user's batch jobs
   */
  async getUserBatchJobs(
    userId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{ jobs: BatchJob[]; total: number }> {
    
    try {
      const offset = (page - 1) * limit;
      
      // Get from database
      const jobs = await db('batch_jobs')
        .where({ user_id: userId })
        .orderBy('created_at', 'desc')
        .limit(limit)
        .offset(offset);

      const [{ count }] = await db('batch_jobs')
        .where({ user_id: userId })
        .count('* as count');

      const batchJobs = jobs.map(job => this.mapDatabaseToBatchJob(job));

      return {
        jobs: batchJobs,
        total: parseInt(count as string)
      };

    } catch (error) {
      logger.error('Failed to get user batch jobs:', error);
      return { jobs: [], total: 0 };
    }
  }

  /**
   * Cancel batch job
   */
  async cancelBatchJob(jobId: string, userId: string): Promise<boolean> {
    try {
      const job = this.activeJobs.get(jobId);
      
      if (!job || job.userId !== userId) {
        throw new CustomError('Batch job not found', 404, true, 'JOB_NOT_FOUND');
      }

      if (job.status === 'completed' || job.status === 'failed') {
        throw new CustomError('Cannot cancel completed or failed job', 400, true, 'INVALID_STATUS');
      }

      // Update status
      job.status = 'cancelled';
      
      // Update database
      await db('batch_jobs')
        .where({ id: jobId })
        .update({ 
          status: 'cancelled',
          completed_at: new Date()
        });

      // Remove from active jobs
      this.activeJobs.delete(jobId);

      logger.info(`Batch job cancelled: ${jobId}`);
      return true;

    } catch (error) {
      logger.error('Failed to cancel batch job:', error);
      throw error;
    }
  }

  /**
   * Process batch job asynchronously
   */
  private async processBatchJobAsync(
    jobId: string,
    batchOptions: BatchOptions = {}
  ): Promise<void> {
    
    const job = this.activeJobs.get(jobId);
    if (!job) {
      logger.error(`Batch job not found: ${jobId}`);
      return;
    }

    try {
      logger.info(`Starting batch processing: ${jobId}`);
      
      // Update status
      job.status = 'processing';
      job.startedAt = new Date();
      
      await this.updateBatchJobInDatabase(job);

      const maxConcurrent = batchOptions.maxConcurrent || this.maxConcurrentFiles;
      const results: BatchResult[] = [];

      // Process files in batches
      for (let i = 0; i < job.files.length; i += maxConcurrent) {
        const batch = job.files.slice(i, i + maxConcurrent);
        
        // Process batch concurrently
        const batchPromises = batch.map(file => 
          this.processFileInBatch(job, file, batchOptions)
        );

        const batchResults = await Promise.allSettled(batchPromises);
        
        // Process results
        batchResults.forEach((result, index) => {
          const file = batch[index];
          
          if (result.status === 'fulfilled') {
            results.push(result.value);
            job.progress.completed++;
          } else {
            logger.error(`File processing failed: ${file.originalName}`, result.reason);
            file.status = 'failed';
            file.error = result.reason?.message || 'Unknown error';
            job.progress.failed++;
            
            results.push({
              fileId: file.id,
              fileName: file.originalName,
              success: false,
              processingTime: 0,
              error: file.error
            });
          }
        });

        // Update progress
        await this.updateBatchJobInDatabase(job);
        
        // Check if job was cancelled
        const currentJob = this.activeJobs.get(jobId);
        if (currentJob && currentJob.status === 'cancelled') {
          break;
        }
      }

      // Complete job
      job.status = 'completed';
      job.completedAt = new Date();
      job.results = results;

      await this.updateBatchJobInDatabase(job);
      
      // Remove from active jobs
      this.activeJobs.delete(jobId);

      logger.info(`Batch job completed: ${jobId} (${job.progress.completed}/${job.progress.total} successful)`);

    } catch (error) {
      logger.error(`Batch job failed: ${jobId}`, error);
      
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date();
      
      await this.updateBatchJobInDatabase(job);
      this.activeJobs.delete(jobId);
    }
  }

  /**
   * Process single file in batch
   */
  private async processFileInBatch(
    job: BatchJob,
    file: BatchFile,
    batchOptions: BatchOptions
  ): Promise<BatchResult> {
    
    const startTime = Date.now();
    
    try {
      logger.info(`Processing file in batch: ${file.originalName}`);
      
      file.status = 'processing';
      job.progress.currentFile = file.originalName;
      
      // Validate PDF
      const isValid = await PDFProcessingService.validatePDF(file.filePath);
      if (!isValid) {
        throw new Error('Invalid PDF file');
      }

      // Process with PDF service
      const result = await this.pdfProcessor.processPDF(
        file.filePath,
        job.options,
        (progress) => {
          // Log progress for individual file
          logger.debug(`File ${file.originalName} progress: ${progress.stage} - ${progress.progress}%`);
        }
      );

      if (result.success && result.outputPath) {
        file.status = 'completed';
        file.outputPath = result.outputPath;
        file.processingTime = result.processingTime;

        return {
          fileId: file.id,
          fileName: file.originalName,
          success: true,
          outputPath: result.outputPath,
          processingTime: result.processingTime,
          complexity: result.complexity
        };
      } else {
        throw new Error(result.error || 'Processing failed');
      }

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      file.status = 'failed';
      file.error = errorMessage;
      file.processingTime = processingTime;

      throw new Error(errorMessage);
    }
  }

  /**
   * Save batch job to database
   */
  private async saveBatchJobToDatabase(job: BatchJob): Promise<void> {
    try {
      await db('batch_jobs').insert({
        id: job.id,
        user_id: job.userId,
        name: job.name,
        status: job.status,
        total_files: job.progress.total,
        completed_files: job.progress.completed,
        failed_files: job.progress.failed,
        options: JSON.stringify(job.options),
        files: JSON.stringify(job.files),
        created_at: job.createdAt
      });
    } catch (error) {
      logger.error('Failed to save batch job to database:', error);
      throw error;
    }
  }

  /**
   * Update batch job in database
   */
  private async updateBatchJobInDatabase(job: BatchJob): Promise<void> {
    try {
      await db('batch_jobs')
        .where({ id: job.id })
        .update({
          status: job.status,
          completed_files: job.progress.completed,
          failed_files: job.progress.failed,
          files: JSON.stringify(job.files),
          results: job.results ? JSON.stringify(job.results) : null,
          started_at: job.startedAt,
          completed_at: job.completedAt,
          error_message: job.error
        });
    } catch (error) {
      logger.error('Failed to update batch job in database:', error);
    }
  }

  /**
   * Get batch job from database
   */
  private async getBatchJobFromDatabase(jobId: string, userId: string): Promise<BatchJob | null> {
    try {
      const job = await db('batch_jobs')
        .where({ id: jobId, user_id: userId })
        .first();

      return job ? this.mapDatabaseToBatchJob(job) : null;
    } catch (error) {
      logger.error('Failed to get batch job from database:', error);
      return null;
    }
  }

  /**
   * Map database record to BatchJob
   */
  private mapDatabaseToBatchJob(dbJob: any): BatchJob {
    return {
      id: dbJob.id,
      userId: dbJob.user_id,
      name: dbJob.name,
      files: JSON.parse(dbJob.files || '[]'),
      options: JSON.parse(dbJob.options || '{}'),
      status: dbJob.status,
      progress: {
        total: dbJob.total_files,
        completed: dbJob.completed_files,
        failed: dbJob.failed_files
      },
      createdAt: dbJob.created_at,
      startedAt: dbJob.started_at,
      completedAt: dbJob.completed_at,
      results: dbJob.results ? JSON.parse(dbJob.results) : undefined,
      error: dbJob.error_message
    };
  }
}
