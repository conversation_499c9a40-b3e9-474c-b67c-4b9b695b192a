import * as ExcelJS from 'exceljs';
import * as path from 'path';
import * as fs from 'fs/promises';
import { logger } from '../utils/logger';
import { ProcessingOptions } from './PDFProcessingService';

export interface ExcelGenerationOptions {
  includeMetadata?: boolean;
  autoFitColumns?: boolean;
  addHeaders?: boolean;
  formatNumbers?: boolean;
  addSummary?: boolean;
}

export class ExcelGeneratorService {

  /**
   * Generate Excel file from extracted data
   */
  async generateExcel(
    extractedData: any, 
    originalFilePath: string, 
    options: ProcessingOptions
  ): Promise<string> {
    
    try {
      logger.info('Starting Excel generation...');

      const workbook = new ExcelJS.Workbook();
      
      // Set workbook properties
      this.setWorkbookProperties(workbook, originalFilePath);

      // Add main data worksheet
      await this.addDataWorksheet(workbook, extractedData, options);

      // Add metadata worksheet if requested
      if (options.extractText) {
        await this.addMetadataWorksheet(workbook, extractedData);
      }

      // Generate output file path
      const outputPath = this.generateOutputPath(originalFilePath);
      
      // Write the workbook
      await workbook.xlsx.writeFile(outputPath);
      
      logger.info(`Excel file generated: ${outputPath}`);
      return outputPath;

    } catch (error) {
      logger.error('Excel generation failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to generate Excel file: ${errorMessage}`);
    }
  }

  /**
   * Set workbook metadata properties
   */
  private setWorkbookProperties(workbook: ExcelJS.Workbook, originalFilePath: string): void {
    const fileName = path.basename(originalFilePath, path.extname(originalFilePath));
    
    workbook.creator = 'PDF-Excel SaaS';
    workbook.lastModifiedBy = 'PDF-Excel SaaS';
    workbook.created = new Date();
    workbook.modified = new Date();
    workbook.lastPrinted = new Date();
    
    // Set custom properties (using any to bypass TypeScript strict checking for ExcelJS properties)
    const props = workbook.properties as any;
    props.title = `Converted from ${fileName}`;
    props.subject = 'PDF to Excel Conversion';
    props.keywords = 'PDF, Excel, Conversion, Data Extraction';
    props.category = 'Data Processing';
    props.description = `Excel file generated from PDF: ${fileName}`;
  }

  /**
   * Add main data worksheet
   */
  private async addDataWorksheet(
    workbook: ExcelJS.Workbook, 
    extractedData: any, 
    options: ProcessingOptions
  ): Promise<void> {
    
    const worksheet = workbook.addWorksheet('Extracted Data');
    
    // Configure worksheet
    worksheet.properties.defaultRowHeight = 20;
    worksheet.views = [{ showGridLines: true }];

    let currentRow = 1;

    // Add tables if any were detected
    if (extractedData.tables && extractedData.tables.length > 0) {
      currentRow = await this.addTablesToWorksheet(worksheet, extractedData.tables, currentRow);
    }

    // Add text content if no tables or if text extraction is enabled
    if ((!extractedData.tables || extractedData.tables.length === 0) || options.extractText) {
      currentRow = await this.addTextToWorksheet(worksheet, extractedData.text, currentRow);
    }

    // Auto-fit columns
    this.autoFitColumns(worksheet);

    // Apply formatting
    this.applyWorksheetFormatting(worksheet);
  }

  /**
   * Add tables to worksheet
   */
  private async addTablesToWorksheet(
    worksheet: ExcelJS.Worksheet, 
    tables: any[], 
    startRow: number
  ): Promise<number> {
    
    let currentRow = startRow;

    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      
      // Add table title if multiple tables
      if (tables.length > 1) {
        const titleCell = worksheet.getCell(currentRow, 1);
        titleCell.value = `Table ${i + 1} (Page ${table.position?.page || 'Unknown'})`;
        titleCell.font = { bold: true, size: 14 };
        titleCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE6E6FA' }
        };
        currentRow += 2;
      }

      // Add headers
      if (table.headers && table.headers.length > 0) {
        table.headers.forEach((header: string, colIndex: number) => {
          const cell = worksheet.getCell(currentRow, colIndex + 1);
          cell.value = header;
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF0F8FF' }
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
        currentRow++;
      }

      // Add data rows
      if (table.rows && table.rows.length > 0) {
        table.rows.forEach((row: string[]) => {
          row.forEach((cellValue: string, colIndex: number) => {
            const cell = worksheet.getCell(currentRow, colIndex + 1);
            
            // Try to parse as number if it looks numeric
            const numericValue = this.parseNumericValue(cellValue);
            cell.value = numericValue !== null ? numericValue : cellValue;
            
            // Apply cell formatting
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };
            
            // Format numbers
            if (numericValue !== null) {
              cell.numFmt = '#,##0.00';
            }
          });
          currentRow++;
        });
      }

      // Add spacing between tables
      currentRow += 2;
    }

    return currentRow;
  }

  /**
   * Add text content to worksheet
   */
  private async addTextToWorksheet(
    worksheet: ExcelJS.Worksheet, 
    textItems: any[], 
    startRow: number
  ): Promise<number> {
    
    let currentRow = startRow;

    if (!textItems || textItems.length === 0) {
      const cell = worksheet.getCell(currentRow, 1);
      cell.value = 'No text content extracted';
      cell.font = { italic: true };
      return currentRow + 1;
    }

    // Add section title
    const titleCell = worksheet.getCell(currentRow, 1);
    titleCell.value = 'Extracted Text Content';
    titleCell.font = { bold: true, size: 14 };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFEFD5' }
    };
    currentRow += 2;

    // Group text by pages
    const pageGroups = this.groupTextByPages(textItems);

    for (const [pageNum, pageText] of pageGroups.entries()) {
      // Add page header
      const pageHeaderCell = worksheet.getCell(currentRow, 1);
      pageHeaderCell.value = `Page ${pageNum + 1}`;
      pageHeaderCell.font = { bold: true };
      currentRow++;

      // Add text content
      const combinedText = pageText.map(item => item.content).join(' ');
      const textCell = worksheet.getCell(currentRow, 1);
      textCell.value = combinedText;
      textCell.alignment = { wrapText: true, vertical: 'top' };
      
      // Set row height for wrapped text
      worksheet.getRow(currentRow).height = Math.min(Math.max(combinedText.length / 50, 20), 100);
      
      currentRow += 2;
    }

    return currentRow;
  }

  /**
   * Add metadata worksheet
   */
  private async addMetadataWorksheet(workbook: ExcelJS.Workbook, extractedData: any): Promise<void> {
    const worksheet = workbook.addWorksheet('Metadata');
    
    const metadata = extractedData.metadata || {};
    
    // Add metadata information
    const metadataItems = [
      ['Extraction Method', metadata.extractionMethod || 'Unknown'],
      ['Total Pages', metadata.totalPages || 'Unknown'],
      ['Processing Time (ms)', metadata.processingTime || 'Unknown'],
      ['Extraction Date', new Date().toISOString()],
      ['Tables Detected', extractedData.tables?.length || 0],
      ['Text Items Extracted', extractedData.text?.length || 0]
    ];

    metadataItems.forEach((item, index) => {
      const row = index + 1;
      worksheet.getCell(row, 1).value = item[0];
      worksheet.getCell(row, 2).value = item[1];
      
      // Format header column
      worksheet.getCell(row, 1).font = { bold: true };
    });

    // Auto-fit columns
    worksheet.getColumn(1).width = 20;
    worksheet.getColumn(2).width = 30;
  }

  /**
   * Generate output file path
   */
  private generateOutputPath(originalFilePath: string): string {
    const dir = path.dirname(originalFilePath);
    const baseName = path.basename(originalFilePath, path.extname(originalFilePath));
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    
    return path.join(dir, `${baseName}_converted_${timestamp}.xlsx`);
  }

  /**
   * Auto-fit columns based on content
   */
  private autoFitColumns(worksheet: ExcelJS.Worksheet): void {
    worksheet.columns.forEach((column, index) => {
      let maxLength = 10; // Minimum width
      
      column.eachCell?.({ includeEmpty: false }, (cell) => {
        const cellValue = cell.value?.toString() || '';
        maxLength = Math.max(maxLength, cellValue.length);
      });
      
      // Set column width with reasonable limits
      column.width = Math.min(Math.max(maxLength + 2, 10), 50);
    });
  }

  /**
   * Apply general worksheet formatting
   */
  private applyWorksheetFormatting(worksheet: ExcelJS.Worksheet): void {
    // Set default font
    worksheet.eachRow((row) => {
      row.eachCell((cell) => {
        if (!cell.font) {
          cell.font = { name: 'Calibri', size: 11 };
        }
      });
    });

    // Freeze first row if it contains headers
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];
  }

  /**
   * Parse numeric values from strings
   */
  private parseNumericValue(value: string): number | null {
    if (!value || typeof value !== 'string') return null;
    
    // Remove common non-numeric characters
    const cleaned = value.replace(/[,\s$€%]/g, '');
    
    // Check if it's a valid number
    const parsed = parseFloat(cleaned);
    return !isNaN(parsed) && isFinite(parsed) ? parsed : null;
  }

  /**
   * Group text items by pages
   */
  private groupTextByPages(textItems: any[]): any[][] {
    const pages: any[][] = [];
    
    textItems.forEach(item => {
      const pageIndex = (item.page || 1) - 1;
      if (!pages[pageIndex]) {
        pages[pageIndex] = [];
      }
      pages[pageIndex].push(item);
    });

    return pages;
  }
}
