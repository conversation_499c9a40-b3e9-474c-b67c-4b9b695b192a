import { PDFDocument } from 'pdf-lib';
import * as pdfjsLib from 'pdfjs-dist';
import { logger } from '../utils/logger';

export interface ExtractedText {
  content: string;
  page: number;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  fontSize: number;
  fontName: string;
}

export interface TableData {
  headers: string[];
  rows: string[][];
  position: {
    page: number;
    x: number;
    y: number;
  };
  confidence: number;
}

export interface ExtractionResult {
  text: ExtractedText[];
  tables: TableData[];
  metadata: {
    totalPages: number;
    extractionMethod: string;
    processingTime: number;
  };
}

export class TextExtractionService {

  constructor() {
    // Configure PDF.js worker - simplified for now
    // In production, we'd set up the worker properly
    try {
      (pdfjsLib as any).GlobalWorkerOptions.workerSrc = 'pdfjs-dist/build/pdf.worker.js';
    } catch (error) {
      logger.warn('PDF.js worker setup failed, using fallback');
    }
  }

  /**
   * Extract text from PDF document
   */
  async extractText(pdfDoc: PDFDocument, pdfBuffer: Buffer): Promise<ExtractionResult> {
    const startTime = Date.now();
    
    try {
      logger.info('Starting text extraction...');

      // Use PDF.js for better text extraction
      const extractedText = await this.extractWithPDFJS(pdfBuffer);
      
      // Detect tables in the extracted text
      const tables = await this.detectTables(extractedText);

      const processingTime = Date.now() - startTime;
      
      logger.info(`Text extraction completed in ${processingTime}ms`);

      return {
        text: extractedText.text,
        tables,
        metadata: {
          totalPages: pdfDoc.getPageCount(),
          extractionMethod: 'pdfjs',
          processingTime
        }
      };

    } catch (error) {
      logger.error('Text extraction failed:', error);
      
      // Fallback to basic extraction
      return this.fallbackExtraction(pdfDoc, Date.now() - startTime);
    }
  }

  /**
   * Extract text using PDF.js for better accuracy
   */
  private async extractWithPDFJS(pdfBuffer: Buffer): Promise<{ text: ExtractedText[] }> {
    const extractedText: ExtractedText[] = [];

    try {
      const loadingTask = pdfjsLib.getDocument({ data: pdfBuffer });
      const pdf = await loadingTask.promise;

      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        
        // Process each text item
        textContent.items.forEach((item: any, index: number) => {
          if (item.str && item.str.trim()) {
            extractedText.push({
              content: item.str,
              page: pageNum,
              position: {
                x: item.transform[4] || 0,
                y: item.transform[5] || 0,
                width: item.width || 0,
                height: item.height || 0
              },
              fontSize: item.height || 12,
              fontName: item.fontName || 'unknown'
            });
          }
        });
      }

    } catch (error) {
      logger.error('PDF.js extraction failed:', error);
      throw error;
    }

    return { text: extractedText };
  }

  /**
   * Detect tables in extracted text
   */
  async detectTables(extractionResult: any): Promise<TableData[]> {
    const tables: TableData[] = [];
    
    try {
      // Group text by pages
      const pageTexts = this.groupTextByPages(extractionResult.text || []);
      
      // Analyze each page for table patterns
      for (const [pageNum, pageText] of pageTexts.entries()) {
        const pageTables = this.detectTablesInPage(pageText, pageNum + 1);
        tables.push(...pageTables);
      }

    } catch (error) {
      logger.error('Table detection failed:', error);
    }

    return tables;
  }

  /**
   * Group extracted text by pages
   */
  private groupTextByPages(textItems: ExtractedText[]): ExtractedText[][] {
    const pages: ExtractedText[][] = [];
    
    textItems.forEach(item => {
      const pageIndex = item.page - 1;
      if (!pages[pageIndex]) {
        pages[pageIndex] = [];
      }
      pages[pageIndex].push(item);
    });

    return pages;
  }

  /**
   * Detect tables in a single page
   */
  private detectTablesInPage(pageText: ExtractedText[], pageNum: number): TableData[] {
    const tables: TableData[] = [];
    
    if (!pageText || pageText.length === 0) {
      return tables;
    }

    // Sort text items by position (top to bottom, left to right)
    const sortedText = pageText.sort((a, b) => {
      if (Math.abs(a.position.y - b.position.y) < 5) {
        return a.position.x - b.position.x;
      }
      return b.position.y - a.position.y; // PDF coordinates are bottom-up
    });

    // Group text into rows based on Y position
    const rows = this.groupTextIntoRows(sortedText);
    
    // Detect table patterns
    const tableCandidate = this.analyzeRowsForTable(rows, pageNum);
    
    if (tableCandidate) {
      tables.push(tableCandidate);
    }

    return tables;
  }

  /**
   * Group text items into rows based on Y position
   */
  private groupTextIntoRows(textItems: ExtractedText[]): ExtractedText[][] {
    const rows: ExtractedText[][] = [];
    const tolerance = 5; // Y position tolerance for same row

    textItems.forEach(item => {
      let addedToRow = false;
      
      for (const row of rows) {
        if (row.length > 0) {
          const rowY = row[0].position.y;
          if (Math.abs(item.position.y - rowY) <= tolerance) {
            row.push(item);
            addedToRow = true;
            break;
          }
        }
      }
      
      if (!addedToRow) {
        rows.push([item]);
      }
    });

    // Sort items within each row by X position
    rows.forEach(row => {
      row.sort((a, b) => a.position.x - b.position.x);
    });

    return rows;
  }

  /**
   * Analyze rows to detect table structure
   */
  private analyzeRowsForTable(rows: ExtractedText[][], pageNum: number): TableData | null {
    if (rows.length < 2) {
      return null; // Need at least 2 rows for a table
    }

    // Look for consistent column structure
    const columnCounts = rows.map(row => row.length);
    const avgColumns = columnCounts.reduce((a, b) => a + b, 0) / columnCounts.length;
    
    // Filter rows that have similar column counts (within 1 of average)
    const tableRows = rows.filter(row => Math.abs(row.length - avgColumns) <= 1);
    
    if (tableRows.length < 2) {
      return null;
    }

    // Extract headers (first row) and data rows
    const headers = tableRows[0].map(item => item.content.trim());
    const dataRows = tableRows.slice(1).map(row => 
      row.map(item => item.content.trim())
    );

    // Calculate confidence based on structure consistency
    const confidence = this.calculateTableConfidence(tableRows);
    
    if (confidence < 0.5) {
      return null; // Low confidence, probably not a table
    }

    return {
      headers,
      rows: dataRows,
      position: {
        page: pageNum,
        x: Math.min(...tableRows[0].map(item => item.position.x)),
        y: Math.max(...tableRows[0].map(item => item.position.y))
      },
      confidence
    };
  }

  /**
   * Calculate confidence score for table detection
   */
  private calculateTableConfidence(rows: ExtractedText[][]): number {
    if (rows.length < 2) return 0;

    let score = 0;
    const maxScore = 100;

    // Check column count consistency
    const columnCounts = rows.map(row => row.length);
    const avgColumns = columnCounts.reduce((a, b) => a + b, 0) / columnCounts.length;
    const columnConsistency = columnCounts.filter(count => Math.abs(count - avgColumns) <= 1).length / columnCounts.length;
    score += columnConsistency * 40;

    // Check row alignment
    const alignmentScore = this.calculateRowAlignment(rows);
    score += alignmentScore * 30;

    // Check for numeric data (common in tables)
    const numericScore = this.calculateNumericContent(rows);
    score += numericScore * 20;

    // Check minimum requirements
    if (rows.length >= 3 && avgColumns >= 2) {
      score += 10;
    }

    return Math.min(score, maxScore) / maxScore;
  }

  /**
   * Calculate row alignment score
   */
  private calculateRowAlignment(rows: ExtractedText[][]): number {
    if (rows.length < 2) return 0;

    let alignmentScore = 0;
    const tolerance = 10;

    // Check if columns are vertically aligned
    for (let col = 0; col < Math.min(...rows.map(r => r.length)); col++) {
      const xPositions = rows.map(row => row[col]?.position.x).filter(x => x !== undefined);
      if (xPositions.length < 2) continue;

      const avgX = xPositions.reduce((a, b) => a + b, 0) / xPositions.length;
      const aligned = xPositions.filter(x => Math.abs(x - avgX) <= tolerance).length;
      alignmentScore += aligned / xPositions.length;
    }

    return alignmentScore / Math.min(...rows.map(r => r.length));
  }

  /**
   * Calculate numeric content score
   */
  private calculateNumericContent(rows: ExtractedText[][]): number {
    let totalCells = 0;
    let numericCells = 0;

    rows.forEach(row => {
      row.forEach(cell => {
        totalCells++;
        if (this.isNumericContent(cell.content)) {
          numericCells++;
        }
      });
    });

    return totalCells > 0 ? numericCells / totalCells : 0;
  }

  /**
   * Check if content is numeric
   */
  private isNumericContent(content: string): boolean {
    const cleaned = content.replace(/[,.\s$%€]/g, '');
    return /^\d+$/.test(cleaned) && cleaned.length > 0;
  }

  /**
   * Fallback extraction method
   */
  private fallbackExtraction(pdfDoc: PDFDocument, processingTime: number): ExtractionResult {
    logger.info('Using fallback text extraction');
    
    return {
      text: [{
        content: 'Text extraction failed - using fallback method',
        page: 1,
        position: { x: 0, y: 0, width: 0, height: 0 },
        fontSize: 12,
        fontName: 'unknown'
      }],
      tables: [],
      metadata: {
        totalPages: pdfDoc.getPageCount(),
        extractionMethod: 'fallback',
        processingTime
      }
    };
  }
}
