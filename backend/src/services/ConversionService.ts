import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import { db } from '../config/database';
import { CustomError } from '../middleware/errorHandler';
import { UserService } from './UserService';
import { logger } from '../utils/logger';
import { PDFProcessingService, ProcessingOptions as PDFProcessingOptions, ProcessingProgress } from './PDFProcessingService';

export interface ConversionOptions {
  template?: string;
  outputFormat?: 'xlsx' | 'csv';
  extractTables?: boolean;
  extractText?: boolean;
  ocrEnabled?: boolean;
}

export interface ConversionData {
  userId: string;
  file: Express.Multer.File;
  options: ConversionOptions;
}

export interface ConversionResult {
  id: string;
  fileName: string;
  fileSize: number;
  status: string;
  createdAt: Date;
  processingTime?: number;
  resultPath?: string;
}

export interface ConversionsResponse {
  conversions: ConversionResult[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class ConversionService {
  private userService = new UserService();
  private pdfProcessor = new PDFProcessingService();

  /**
   * Start PDF conversion process
   */
  async startConversion(data: ConversionData): Promise<ConversionResult> {
    const { userId, file, options } = data;

    // Create conversion record
    const conversionId = uuidv4();
    const [conversion] = await db('conversions')
      .insert({
        id: conversionId,
        user_id: userId,
        file_name: file.originalname,
        file_size: file.size,
        file_path: file.path,
        status: 'pending',
        metadata: JSON.stringify(options)
      })
      .returning(['id', 'file_name', 'file_size', 'status', 'created_at']);

    logger.info(`Conversion started: ${conversionId} for user: ${userId}`);

    // Start async processing (this will be replaced with actual AI processing in Phase 3)
    this.processConversionAsync(conversionId, file, options);

    return {
      id: conversion.id,
      fileName: conversion.file_name,
      fileSize: conversion.file_size,
      status: conversion.status,
      createdAt: conversion.created_at
    };
  }

  /**
   * Get user's conversions with pagination
   */
  async getConversions(userId: string, filters: {
    page: number;
    limit: number;
    status?: string;
  }): Promise<ConversionsResponse> {
    const { page, limit, status } = filters;
    const offset = (page - 1) * limit;

    let query = db('conversions')
      .where({ user_id: userId })
      .orderBy('created_at', 'desc');

    if (status) {
      query = query.where({ status });
    }

    // Get total count
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('* as count');
    const total = parseInt(count as string);

    // Get paginated results
    const conversions = await query
      .limit(limit)
      .offset(offset)
      .select(['id', 'file_name', 'file_size', 'status', 'created_at', 'processing_time', 'result_path']);

    return {
      conversions: conversions.map(c => ({
        id: c.id,
        fileName: c.file_name,
        fileSize: c.file_size,
        status: c.status,
        createdAt: c.created_at,
        processingTime: c.processing_time,
        resultPath: c.result_path
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Get specific conversion
   */
  async getConversion(conversionId: string, userId: string): Promise<ConversionResult> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    return {
      id: conversion.id,
      fileName: conversion.file_name,
      fileSize: conversion.file_size,
      status: conversion.status,
      createdAt: conversion.created_at,
      processingTime: conversion.processing_time,
      resultPath: conversion.result_path
    };
  }

  /**
   * Get download path for converted file
   */
  async getDownloadPath(conversionId: string, userId: string): Promise<string> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    if (conversion.status !== 'completed' || !conversion.result_path) {
      throw new CustomError('Conversion not completed or file not available', 400, true, 'FILE_NOT_READY');
    }

    // Check if file exists
    try {
      await fs.access(conversion.result_path);
      return conversion.result_path;
    } catch (error) {
      throw new CustomError('File not found', 404, true, 'FILE_NOT_FOUND');
    }
  }

  /**
   * Delete conversion and associated files
   */
  async deleteConversion(conversionId: string, userId: string): Promise<void> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    // Delete files
    try {
      await fs.unlink(conversion.file_path);
      if (conversion.result_path) {
        await fs.unlink(conversion.result_path);
      }
    } catch (error) {
      logger.warn(`Failed to delete files for conversion ${conversionId}:`, error);
    }

    // Delete database record
    await db('conversions').where({ id: conversionId }).del();

    logger.info(`Conversion deleted: ${conversionId}`);
  }

  /**
   * Retry failed conversion
   */
  async retryConversion(conversionId: string, userId: string): Promise<ConversionResult> {
    const conversion = await db('conversions')
      .where({ id: conversionId, user_id: userId })
      .first();

    if (!conversion) {
      throw new CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
    }

    if (conversion.status !== 'failed') {
      throw new CustomError('Only failed conversions can be retried', 400, true, 'INVALID_STATUS');
    }

    // Update status to pending
    await db('conversions')
      .where({ id: conversionId })
      .update({
        status: 'pending',
        error_message: null
      });

    // Restart processing
    const file = {
      originalname: conversion.file_name,
      path: conversion.file_path,
      size: conversion.file_size
    } as Express.Multer.File;

    const options = conversion.metadata ? JSON.parse(conversion.metadata) : {};
    this.processConversionAsync(conversionId, file, options);

    logger.info(`Conversion retry started: ${conversionId}`);

    return {
      id: conversion.id,
      fileName: conversion.file_name,
      fileSize: conversion.file_size,
      status: 'pending',
      createdAt: conversion.created_at
    };
  }

  /**
   * Real AI-powered PDF processing
   */
  private async processConversionAsync(conversionId: string, file: Express.Multer.File, options: ConversionOptions): Promise<void> {
    try {
      logger.info(`Starting AI processing for conversion: ${conversionId}`);

      // Update status to processing
      await db('conversions')
        .where({ id: conversionId })
        .update({ status: 'processing' });

      // Validate PDF file first
      const isValidPDF = await PDFProcessingService.validatePDF(file.path);
      if (!isValidPDF) {
        throw new Error('Invalid PDF file format');
      }

      // Convert ConversionOptions to PDFProcessingOptions
      const processingOptions: PDFProcessingOptions = {
        template: options.template,
        outputFormat: options.outputFormat || 'xlsx',
        extractTables: options.extractTables !== false, // Default to true
        extractText: options.extractText !== false, // Default to true
        ocrEnabled: options.ocrEnabled || false
      };

      // Process PDF with progress tracking
      const result = await this.pdfProcessor.processPDF(
        file.path,
        processingOptions,
        (progress: ProcessingProgress) => {
          // Log progress for debugging
          logger.info(`Conversion ${conversionId} progress: ${progress.stage} - ${progress.progress}% - ${progress.message}`);

          // In future versions, we could emit real-time progress updates via WebSocket
          // For now, we just log the progress
        }
      );

      if (result.success && result.outputPath) {
        // Update conversion as completed
        await db('conversions')
          .where({ id: conversionId })
          .update({
            status: 'completed',
            processing_time: result.processingTime,
            result_path: result.outputPath,
            metadata: JSON.stringify({
              ...options,
              complexity: result.complexity,
              method: result.method,
              extractedTables: result.extractedData?.tables?.length || 0,
              extractedTextItems: result.extractedData?.text?.length || 0
            })
          });

        // Track usage
        const conversion = await db('conversions').where({ id: conversionId }).first();
        if (conversion) {
          await this.userService.trackUsage(
            conversion.user_id,
            file.size,
            result.processingTime
          );
        }

        logger.info(`AI conversion completed: ${conversionId} (${result.complexity} complexity, ${result.method} processing)`);

      } else {
        throw new Error(result.error || 'PDF processing failed');
      }

    } catch (error) {
      logger.error(`AI conversion failed: ${conversionId}`, error);

      // Update conversion as failed
      await db('conversions')
        .where({ id: conversionId })
        .update({
          status: 'failed',
          error_message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
  }
}
