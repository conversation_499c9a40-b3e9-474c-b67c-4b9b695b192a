import * as jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../config/database';
import { EncryptionService } from '../config/security';
import { CustomError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

export interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface LoginResult {
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    subscriptionPlan: string;
    isVerified: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
  private readonly JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
  private readonly ACCESS_TOKEN_EXPIRY = process.env.JWT_ACCESS_EXPIRY || '15m';
  private readonly REFRESH_TOKEN_EXPIRY = process.env.JWT_REFRESH_EXPIRY || '7d';

  /**
   * Register a new user
   */
  async register(data: RegisterData): Promise<LoginResult> {
    const { email, password, firstName, lastName } = data;

    // Check if user already exists
    const existingUser = await db('users').where({ email }).first();
    if (existingUser) {
      throw new CustomError('User with this email already exists', 409, true, 'USER_EXISTS');
    }

    // Hash password
    const passwordHash = await EncryptionService.hashPassword(password);

    // Generate verification token
    const verificationToken = EncryptionService.generateSecureToken();

    // Create user
    const [user] = await db('users')
      .insert({
        id: uuidv4(),
        email,
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        verification_token: verificationToken,
        subscription_plan: 'free',
        is_verified: false
      })
      .returning(['id', 'email', 'first_name', 'last_name', 'subscription_plan', 'is_verified']);

    logger.info(`User registered successfully: ${email}`);

    // Generate tokens
    const tokens = await this.generateTokens(user.id);

    // TODO: Send verification email
    // await this.sendVerificationEmail(email, verificationToken);

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        subscriptionPlan: user.subscription_plan,
        isVerified: user.is_verified
      },
      tokens
    };
  }

  /**
   * Login user
   */
  async login(email: string, password: string): Promise<LoginResult> {
    // Find user
    const user = await db('users').where({ email }).first();
    if (!user) {
      throw new CustomError('Invalid credentials', 401, true, 'INVALID_CREDENTIALS');
    }

    // Check password
    const isPasswordValid = await EncryptionService.comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw new CustomError('Invalid credentials', 401, true, 'INVALID_CREDENTIALS');
    }

    logger.info(`User logged in successfully: ${email}`);

    // Generate tokens
    const tokens = await this.generateTokens(user.id);

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        subscriptionPlan: user.subscription_plan,
        isVerified: user.is_verified
      },
      tokens
    };
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ tokens: { accessToken: string; refreshToken: string } }> {
    try {
      const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET) as { userId: string };
      
      // Check if user exists
      const user = await db('users').where({ id: decoded.userId }).first();
      if (!user) {
        throw new CustomError('Invalid refresh token', 401, true, 'INVALID_TOKEN');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user.id);

      return { tokens };
    } catch (error) {
      throw new CustomError('Invalid refresh token', 401, true, 'INVALID_TOKEN');
    }
  }

  /**
   * Logout user (invalidate refresh token)
   */
  async logout(refreshToken: string): Promise<void> {
    // TODO: Implement token blacklisting with Redis
    // For now, we'll just verify the token is valid
    try {
      jwt.verify(refreshToken, this.JWT_REFRESH_SECRET);
    } catch (error) {
      throw new CustomError('Invalid refresh token', 401, true, 'INVALID_TOKEN');
    }
  }

  /**
   * Verify email
   */
  async verifyEmail(token: string): Promise<void> {
    const user = await db('users').where({ verification_token: token }).first();
    if (!user) {
      throw new CustomError('Invalid verification token', 400, true, 'INVALID_TOKEN');
    }

    await db('users')
      .where({ id: user.id })
      .update({
        is_verified: true,
        verification_token: null
      });

    logger.info(`Email verified for user: ${user.email}`);
  }

  /**
   * Send password reset email
   */
  async forgotPassword(email: string): Promise<void> {
    const user = await db('users').where({ email }).first();
    if (!user) {
      // Don't reveal if email exists
      return;
    }

    const resetToken = EncryptionService.generateSecureToken();
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour

    await db('users')
      .where({ id: user.id })
      .update({
        reset_token: resetToken,
        reset_token_expires: resetTokenExpires
      });

    // TODO: Send password reset email
    // await this.sendPasswordResetEmail(email, resetToken);

    logger.info(`Password reset requested for user: ${email}`);
  }

  /**
   * Reset password
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    const user = await db('users')
      .where({ reset_token: token })
      .where('reset_token_expires', '>', new Date())
      .first();

    if (!user) {
      throw new CustomError('Invalid or expired reset token', 400, true, 'INVALID_TOKEN');
    }

    const passwordHash = await EncryptionService.hashPassword(newPassword);

    await db('users')
      .where({ id: user.id })
      .update({
        password_hash: passwordHash,
        reset_token: null,
        reset_token_expires: null
      });

    logger.info(`Password reset successfully for user: ${user.email}`);
  }

  /**
   * Generate JWT tokens
   */
  private async generateTokens(userId: string): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = { userId };

    // @ts-ignore
    const accessToken = jwt.sign(
      payload,
      this.JWT_SECRET,
      { expiresIn: this.ACCESS_TOKEN_EXPIRY }
    );

    // @ts-ignore
    const refreshToken = jwt.sign(
      payload,
      this.JWT_REFRESH_SECRET,
      { expiresIn: this.REFRESH_TOKEN_EXPIRY }
    );

    return {
      accessToken: accessToken as string,
      refreshToken: refreshToken as string
    };
  }

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): { userId: string } {
    try {
      return jwt.verify(token, this.JWT_SECRET) as { userId: string };
    } catch (error) {
      throw new CustomError('Invalid access token', 401, true, 'INVALID_TOKEN');
    }
  }
}
