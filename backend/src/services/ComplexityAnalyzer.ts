import { PDFDocument } from 'pdf-lib';
import { logger } from '../utils/logger';

export interface ComplexityMetrics {
  pageCount: number;
  textDensity: number;
  imageCount: number;
  tableCount: number;
  fontVariations: number;
  layoutComplexity: number;
  hasScannedContent: boolean;
  hasFormFields: boolean;
  hasAnnotations: boolean;
  fileSize: number;
}

export interface ComplexityResult {
  level: 'simple' | 'medium' | 'complex';
  score: number;
  metrics: ComplexityMetrics;
  recommendations: string[];
  estimatedProcessingTime: number;
}

export class ComplexityAnalyzer {
  
  /**
   * Analyze PDF document complexity
   */
  async analyze(pdfDoc: PDFDocument, pdfBuffer: Buffer): Promise<ComplexityResult> {
    try {
      logger.info('Starting complexity analysis...');
      
      const metrics = await this.extractMetrics(pdfDoc, pdfBuffer);
      const score = this.calculateComplexityScore(metrics);
      const level = this.determineComplexityLevel(score);
      const recommendations = this.generateRecommendations(metrics, level);
      const estimatedProcessingTime = this.estimateProcessingTime(metrics, level);

      logger.info(`Complexity analysis completed: ${level} (score: ${score})`);

      return {
        level,
        score,
        metrics,
        recommendations,
        estimatedProcessingTime
      };

    } catch (error) {
      logger.error('Complexity analysis failed:', error);
      
      // Return default simple complexity on error
      return {
        level: 'simple',
        score: 0,
        metrics: this.getDefaultMetrics(),
        recommendations: ['Document analysis failed, using basic processing'],
        estimatedProcessingTime: 5000
      };
    }
  }

  /**
   * Extract complexity metrics from PDF
   */
  private async extractMetrics(pdfDoc: PDFDocument, pdfBuffer: Buffer): Promise<ComplexityMetrics> {
    const pages = pdfDoc.getPages();
    const pageCount = pages.length;
    const fileSize = pdfBuffer.length;

    // Basic metrics we can extract with pdf-lib
    let textDensity = 0;
    let imageCount = 0;
    let tableCount = 0;
    let fontVariations = 0;
    let layoutComplexity = 0;

    // Analyze each page
    for (const page of pages) {
      // Get page content and dimensions
      const { width, height } = page.getSize();
      const pageArea = width * height;

      // Estimate text density (simplified)
      // In a real implementation, we'd extract actual text content
      textDensity += this.estimateTextDensity(page, pageArea);

      // Count images (simplified estimation)
      imageCount += this.estimateImageCount(page);

      // Detect potential tables (simplified heuristic)
      tableCount += this.estimateTableCount(page);

      // Estimate layout complexity
      layoutComplexity += this.estimateLayoutComplexity(page, width, height);
    }

    // Average metrics per page
    textDensity = textDensity / pageCount;
    layoutComplexity = layoutComplexity / pageCount;

    // Detect scanned content (heuristic based on file size vs page count)
    const hasScannedContent = this.detectScannedContent(fileSize, pageCount);

    // Check for form fields and annotations
    const form = pdfDoc.getForm();
    const hasFormFields = form.getFields().length > 0;
    const hasAnnotations = false; // Simplified - would need deeper analysis

    // Estimate font variations (simplified)
    fontVariations = Math.min(pageCount * 2, 10); // Rough estimate

    return {
      pageCount,
      textDensity,
      imageCount,
      tableCount,
      fontVariations,
      layoutComplexity,
      hasScannedContent,
      hasFormFields,
      hasAnnotations,
      fileSize
    };
  }

  /**
   * Calculate overall complexity score (0-100)
   */
  private calculateComplexityScore(metrics: ComplexityMetrics): number {
    let score = 0;

    // Page count factor (0-20 points)
    score += Math.min(metrics.pageCount * 2, 20);

    // Text density factor (0-15 points)
    score += Math.min(metrics.textDensity * 15, 15);

    // Image count factor (0-15 points)
    score += Math.min(metrics.imageCount * 3, 15);

    // Table count factor (0-20 points)
    score += Math.min(metrics.tableCount * 5, 20);

    // Layout complexity factor (0-15 points)
    score += Math.min(metrics.layoutComplexity * 15, 15);

    // Font variations factor (0-10 points)
    score += Math.min(metrics.fontVariations, 10);

    // Special content factors (0-5 points each)
    if (metrics.hasScannedContent) score += 5;
    if (metrics.hasFormFields) score += 3;
    if (metrics.hasAnnotations) score += 2;

    return Math.min(score, 100);
  }

  /**
   * Determine complexity level based on score
   */
  private determineComplexityLevel(score: number): 'simple' | 'medium' | 'complex' {
    if (score <= 30) return 'simple';
    if (score <= 60) return 'medium';
    return 'complex';
  }

  /**
   * Generate processing recommendations
   */
  private generateRecommendations(metrics: ComplexityMetrics, level: string): string[] {
    const recommendations: string[] = [];

    if (level === 'simple') {
      recommendations.push('Document suitable for fast local processing');
    } else if (level === 'medium') {
      recommendations.push('Document may benefit from enhanced processing');
      if (metrics.tableCount > 3) {
        recommendations.push('Multiple tables detected - using table-specific extraction');
      }
    } else {
      recommendations.push('Complex document - may require specialized processing');
      if (metrics.hasScannedContent) {
        recommendations.push('Scanned content detected - OCR processing recommended');
      }
      if (metrics.imageCount > 5) {
        recommendations.push('Multiple images detected - image analysis may be needed');
      }
    }

    return recommendations;
  }

  /**
   * Estimate processing time in milliseconds
   */
  private estimateProcessingTime(metrics: ComplexityMetrics, level: string): number {
    let baseTime = 2000; // 2 seconds base

    // Add time based on page count
    baseTime += metrics.pageCount * 500;

    // Add time based on complexity level
    if (level === 'medium') baseTime *= 1.5;
    if (level === 'complex') baseTime *= 2.5;

    // Add time for special content
    if (metrics.hasScannedContent) baseTime += 3000;
    if (metrics.tableCount > 0) baseTime += metrics.tableCount * 1000;
    if (metrics.imageCount > 0) baseTime += metrics.imageCount * 500;

    return Math.min(baseTime, 30000); // Cap at 30 seconds
  }

  // Helper methods for metric estimation

  private estimateTextDensity(page: any, pageArea: number): number {
    // Simplified estimation - in real implementation would analyze actual text
    return Math.random() * 0.7 + 0.1; // 0.1 to 0.8
  }

  private estimateImageCount(page: any): number {
    // Simplified estimation - would analyze page resources
    return Math.floor(Math.random() * 3); // 0 to 2 images per page
  }

  private estimateTableCount(page: any): number {
    // Simplified estimation - would analyze text layout patterns
    return Math.floor(Math.random() * 2); // 0 to 1 table per page
  }

  private estimateLayoutComplexity(page: any, width: number, height: number): number {
    // Simplified estimation based on page dimensions and aspect ratio
    const aspectRatio = width / height;
    const isStandardSize = (aspectRatio > 0.7 && aspectRatio < 0.8); // A4-like
    return isStandardSize ? 0.3 : 0.7;
  }

  private detectScannedContent(fileSize: number, pageCount: number): boolean {
    // Heuristic: large file size per page often indicates scanned content
    const sizePerPage = fileSize / pageCount;
    return sizePerPage > 500000; // 500KB per page threshold
  }

  private getDefaultMetrics(): ComplexityMetrics {
    return {
      pageCount: 1,
      textDensity: 0.5,
      imageCount: 0,
      tableCount: 0,
      fontVariations: 1,
      layoutComplexity: 0.3,
      hasScannedContent: false,
      hasFormFields: false,
      hasAnnotations: false,
      fileSize: 0
    };
  }
}
