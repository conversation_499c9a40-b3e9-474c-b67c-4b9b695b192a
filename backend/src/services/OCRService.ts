import { create<PERSON>or<PERSON>, Worker } from 'tesseract.js';
import sharp from 'sharp';
import { PDFDocument } from 'pdf-lib';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/logger';

export interface OCROptions {
  language?: string;
  psm?: number; // Page Segmentation Mode
  oem?: number; // OCR Engine Mode
  whitelist?: string;
  blacklist?: string;
  dpi?: number;
  preprocessImage?: boolean;
}

export interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bbox: {
      x0: number;
      y0: number;
      x1: number;
      y1: number;
    };
  }>;
  lines: Array<{
    text: string;
    confidence: number;
    bbox: {
      x0: number;
      y0: number;
      x1: number;
      y1: number;
    };
  }>;
  processingTime: number;
}

export interface PageOCRResult {
  pageNumber: number;
  result: OCRResult;
  imageData?: Buffer;
}

export class OCRService {
  private worker: Worker | null = null;
  private isInitialized = false;

  constructor() {
    // Worker will be initialized on first use
  }

  /**
   * Initialize OCR worker
   */
  private async initializeWorker(options: OCROptions = {}): Promise<void> {
    if (this.isInitialized && this.worker) {
      return;
    }

    try {
      logger.info('Initializing OCR worker...');
      
      this.worker = await createWorker('eng');

      const language = options.language || 'eng'; // English only for now

      // Configure OCR parameters (simplified)
      await (this.worker as any).setParameters({
        tessedit_pageseg_mode: '1', // Automatic page segmentation with OSD
        tessedit_ocr_engine_mode: '3', // Default, based on what is available
      });

      this.isInitialized = true;
      logger.info('OCR worker initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize OCR worker:', error);
      throw new Error(`OCR initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process PDF with OCR for scanned documents
   */
  async processPDFWithOCR(
    pdfPath: string, 
    options: OCROptions = {}
  ): Promise<PageOCRResult[]> {
    
    const startTime = Date.now();
    
    try {
      logger.info(`Starting OCR processing for PDF: ${pdfPath}`);

      // Initialize worker if needed
      await this.initializeWorker(options);

      // Convert PDF pages to images
      const pageImages = await this.convertPDFToImages(pdfPath, options);
      
      // Process each page with OCR
      const results: PageOCRResult[] = [];
      
      for (let i = 0; i < pageImages.length; i++) {
        const pageImage = pageImages[i];
        logger.info(`Processing page ${i + 1}/${pageImages.length} with OCR...`);
        
        const ocrResult = await this.processImageWithOCR(pageImage, options);
        
        results.push({
          pageNumber: i + 1,
          result: ocrResult,
          imageData: pageImage
        });
      }

      const totalTime = Date.now() - startTime;
      logger.info(`OCR processing completed in ${totalTime}ms for ${results.length} pages`);

      return results;

    } catch (error) {
      logger.error('OCR processing failed:', error);
      throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process single image with OCR
   */
  async processImageWithOCR(
    imageBuffer: Buffer, 
    options: OCROptions = {}
  ): Promise<OCRResult> {
    
    const startTime = Date.now();
    
    try {
      if (!this.worker) {
        await this.initializeWorker(options);
      }

      // Preprocess image if requested
      let processedImage = imageBuffer;
      if (options.preprocessImage !== false) {
        processedImage = await this.preprocessImage(imageBuffer, options);
      }

      // Perform OCR
      const { data } = await this.worker!.recognize(processedImage);
      
      const processingTime = Date.now() - startTime;

      return {
        text: data.text,
        confidence: data.confidence,
        words: data.words.map(word => ({
          text: word.text,
          confidence: word.confidence,
          bbox: word.bbox
        })),
        lines: data.lines.map(line => ({
          text: line.text,
          confidence: line.confidence,
          bbox: line.bbox
        })),
        processingTime
      };

    } catch (error) {
      logger.error('Image OCR processing failed:', error);
      throw new Error(`Image OCR failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert PDF pages to images using pdf2pic
   */
  private async convertPDFToImages(
    pdfPath: string, 
    options: OCROptions = {}
  ): Promise<Buffer[]> {
    
    try {
      // For now, we'll use a simplified approach
      // In production, you'd use pdf2pic or similar library
      logger.info('Converting PDF pages to images...');
      
      // Read PDF file
      const pdfBuffer = await fs.readFile(pdfPath);
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      const pageCount = pdfDoc.getPageCount();
      
      // For this implementation, we'll create placeholder images
      // In real implementation, you'd convert PDF pages to actual images
      const images: Buffer[] = [];
      
      for (let i = 0; i < pageCount; i++) {
        // Create a placeholder image (in real implementation, convert PDF page to image)
        const placeholderImage = await this.createPlaceholderImage(i + 1);
        images.push(placeholderImage);
      }
      
      logger.info(`Converted ${pageCount} PDF pages to images`);
      return images;

    } catch (error) {
      logger.error('PDF to image conversion failed:', error);
      throw new Error(`PDF conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Preprocess image for better OCR accuracy
   */
  private async preprocessImage(
    imageBuffer: Buffer, 
    options: OCROptions = {}
  ): Promise<Buffer> {
    
    try {
      const dpi = options.dpi || 300;
      
      // Use Sharp for image preprocessing
      let processedImage = sharp(imageBuffer)
        .greyscale() // Convert to grayscale
        .normalize() // Normalize contrast
        .sharpen(); // Sharpen for better text recognition

      // Apply additional preprocessing based on options
      if (options.preprocessImage) {
        processedImage = processedImage
          .threshold(128) // Binary threshold
          .median(3); // Noise reduction
      }

      return await processedImage.png().toBuffer();

    } catch (error) {
      logger.error('Image preprocessing failed:', error);
      // Return original image if preprocessing fails
      return imageBuffer;
    }
  }

  /**
   * Create placeholder image (for testing)
   */
  private async createPlaceholderImage(pageNumber: number): Promise<Buffer> {
    try {
      // Create a simple placeholder image with text
      const svg = `
        <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="white"/>
          <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="24" fill="black">
            Placeholder Page ${pageNumber}
            This is sample text for OCR testing.
            In real implementation, this would be the actual PDF page image.
          </text>
        </svg>
      `;

      return await sharp(Buffer.from(svg))
        .png()
        .toBuffer();

    } catch (error) {
      logger.error('Failed to create placeholder image:', error);
      throw error;
    }
  }

  /**
   * Check if document appears to be scanned
   */
  static async isScannedDocument(pdfPath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(pdfPath);
      const pdfBuffer = await fs.readFile(pdfPath);
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      
      const pageCount = pdfDoc.getPageCount();
      const sizePerPage = stats.size / pageCount;
      
      // Heuristic: Large file size per page often indicates scanned content
      // Scanned PDFs typically have larger file sizes due to embedded images
      const isLikelyScanned = sizePerPage > 500000; // 500KB per page threshold
      
      logger.info(`Document analysis: ${sizePerPage} bytes per page, likely scanned: ${isLikelyScanned}`);
      
      return isLikelyScanned;

    } catch (error) {
      logger.error('Failed to analyze document:', error);
      return false;
    }
  }

  /**
   * Get supported languages
   */
  static getSupportedLanguages(): string[] {
    return [
      'eng', // English
      'pol', // Polish
      'deu', // German
      'fra', // French
      'spa', // Spanish
      'ita', // Italian
      'rus', // Russian
      'chi_sim', // Chinese Simplified
      'jpn', // Japanese
      'kor' // Korean
    ];
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.worker) {
      try {
        await this.worker.terminate();
        this.worker = null;
        this.isInitialized = false;
        logger.info('OCR worker terminated');
      } catch (error) {
        logger.error('Failed to terminate OCR worker:', error);
      }
    }
  }
}
