import express, { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import { authenticate } from '../middleware/auth';
import { BatchProcessingService } from '../services/BatchProcessingService';
import { CustomError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { body, query, param, validationResult } from 'express-validator';

// Simple validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
    return;
  }
  next();
};

const router = express.Router();
const batchService = new BatchProcessingService();

// Configure multer for batch file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, process.env.UPLOAD_DIR || 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `batch-${uniqueSuffix}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB per file
    files: 50 // Maximum 50 files per batch
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new CustomError('Only PDF files are allowed', 400, true, 'INVALID_FILE_TYPE'));
    }
  }
});

/**
 * POST /api/batch/create
 * Create a new batch processing job
 */
router.post('/create',
  authenticate,
  upload.array('files', 50),
  [
    body('template').optional().isString().isLength({ max: 100 }),
    body('outputFormat').optional().isIn(['xlsx', 'csv']),
    body('extractTables').optional().isBoolean(),
    body('extractText').optional().isBoolean(),
    body('ocrEnabled').optional().isBoolean(),
    body('maxConcurrent').optional().isInt({ min: 1, max: 5 }),
    body('priority').optional().isIn(['low', 'normal', 'high'])
  ],
  validateRequest,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        throw new CustomError('No files uploaded', 400, true, 'NO_FILES');
      }

      const processingOptions = {
        template: req.body.template,
        outputFormat: req.body.outputFormat || 'xlsx',
        extractTables: req.body.extractTables !== false,
        extractText: req.body.extractText !== false,
        ocrEnabled: req.body.ocrEnabled || false
      };

      const batchOptions = {
        maxConcurrent: parseInt(req.body.maxConcurrent) || 2,
        priority: req.body.priority || 'normal',
        notifyOnComplete: req.body.notifyOnComplete || false,
        retryFailures: req.body.retryFailures || false,
        maxRetries: parseInt(req.body.maxRetries) || 1
      };

      const batchJob = await batchService.createBatchJob(
        req.user!.id,
        files,
        processingOptions,
        batchOptions
      );

      logger.info(`Batch job created: ${batchJob.id} by user ${req.user!.id}`);

      res.status(201).json({
        success: true,
        data: {
          jobId: batchJob.id,
          status: batchJob.status,
          totalFiles: batchJob.progress.total,
          message: 'Batch job created successfully'
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * GET /api/batch/:jobId
 * Get batch job status and details
 */
router.get('/:jobId',
  authenticate,
  [
    param('jobId').isUUID().withMessage('Invalid job ID format')
  ],
  validateRequest,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { jobId } = req.params;
      
      const batchJob = await batchService.getBatchJob(jobId, req.user!.id);
      
      if (!batchJob) {
        throw new CustomError('Batch job not found', 404, true, 'JOB_NOT_FOUND');
      }

      res.json({
        success: true,
        data: batchJob
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * GET /api/batch
 * Get user's batch jobs with pagination
 */
router.get('/',
  authenticate,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    query('status').optional().isIn(['pending', 'processing', 'completed', 'failed', 'cancelled'])
  ],
  validateRequest,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const status = req.query.status as string;

      const result = await batchService.getUserBatchJobs(req.user!.id, page, limit);
      
      // Filter by status if provided
      let filteredJobs = result.jobs;
      if (status) {
        filteredJobs = result.jobs.filter(job => job.status === status);
      }

      res.json({
        success: true,
        data: {
          jobs: filteredJobs,
          pagination: {
            page,
            limit,
            total: result.total,
            pages: Math.ceil(result.total / limit)
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * POST /api/batch/:jobId/cancel
 * Cancel a batch job
 */
router.post('/:jobId/cancel',
  authenticate,
  [
    param('jobId').isUUID().withMessage('Invalid job ID format')
  ],
  validateRequest,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { jobId } = req.params;
      
      const cancelled = await batchService.cancelBatchJob(jobId, req.user!.id);
      
      if (!cancelled) {
        throw new CustomError('Failed to cancel batch job', 400, true, 'CANCEL_FAILED');
      }

      logger.info(`Batch job cancelled: ${jobId} by user ${req.user!.id}`);

      res.json({
        success: true,
        message: 'Batch job cancelled successfully'
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * GET /api/batch/:jobId/download
 * Download batch job results as ZIP
 */
router.get('/:jobId/download',
  authenticate,
  [
    param('jobId').isUUID().withMessage('Invalid job ID format')
  ],
  validateRequest,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { jobId } = req.params;
      
      const batchJob = await batchService.getBatchJob(jobId, req.user!.id);
      
      if (!batchJob) {
        throw new CustomError('Batch job not found', 404, true, 'JOB_NOT_FOUND');
      }

      if (batchJob.status !== 'completed') {
        throw new CustomError('Batch job not completed yet', 400, true, 'JOB_NOT_COMPLETED');
      }

      if (!batchJob.results || batchJob.results.length === 0) {
        throw new CustomError('No results available for download', 404, true, 'NO_RESULTS');
      }

      // For now, return the list of files
      // In a full implementation, you'd create a ZIP file
      const downloadableFiles = batchJob.results
        .filter(result => result.success && result.outputPath)
        .map(result => ({
          fileName: result.fileName.replace('.pdf', '.xlsx'),
          downloadUrl: `/api/conversions/download/${path.basename(result.outputPath!)}`
        }));

      res.json({
        success: true,
        data: {
          jobId,
          totalFiles: downloadableFiles.length,
          files: downloadableFiles,
          message: 'Batch results ready for download'
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * GET /api/batch/stats
 * Get batch processing statistics
 */
router.get('/stats/summary',
  authenticate,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user!.id;
      
      // This would typically query the database for statistics
      // For now, return mock statistics
      const stats = {
        totalBatchJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        totalFilesProcessed: 0,
        averageProcessingTime: 0,
        successRate: 0
      };

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * Error handling middleware specific to batch routes
 */
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size is 50MB per file.',
        code: 'FILE_TOO_LARGE'
      });
    }

    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Maximum 50 files per batch.',
        code: 'TOO_MANY_FILES'
      });
    }
  }

  return next(error);
});

module.exports = router;
