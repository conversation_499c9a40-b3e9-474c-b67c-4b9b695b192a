{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,yDAAsD;AACtD,iDAA6C;AAC7C,iDAAwC;AAkBxC,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;AAK/B,MAAM,YAAY,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACnG,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,0BAAW,CAAC,uBAAuB,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAGtC,MAAM,OAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAGrD,MAAM,IAAI,GAAG,MAAM,IAAA,aAAE,EAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAErE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAW,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACvE,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,UAAU;YAC1B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,UAAU,EAAE,IAAI,CAAC,WAAW;SAC7B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAlCW,QAAA,YAAY,gBAkCvB;AAKK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACvF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,0BAAW,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;IACnF,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACzB,MAAM,IAAI,0BAAW,CAAC,6BAA6B,EAAE,GAAG,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC;IACxF,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAVW,QAAA,eAAe,mBAU1B;AAKK,MAAM,mBAAmB,GAAG,CAAC,YAAsB,EAAE,EAAE;IAC5D,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,0BAAW,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,0BAAW,CAAC,+BAA+B,EAAE,GAAG,EAAE,IAAI,EAAE,uBAAuB,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,mBAAmB,uBAY9B;AAKK,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAC5G,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,0BAAW,CAAC,yBAAyB,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;QACnF,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAG1D,MAAM,KAAK,GAAG,MAAM,IAAA,aAAE,EAAC,gBAAgB,CAAC;aACrC,KAAK,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;aACpD,KAAK,EAAE,CAAC;QAEX,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;QAGzD,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,CAAC;YACP,GAAG,EAAE,QAAQ;YACb,QAAQ,EAAE,QAAQ;SACnB,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAuC,CAAC,IAAI,CAAC,CAAC;QAEhF,IAAI,YAAY,IAAI,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,0BAAW,CAAC,yCAAyC,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAChG,CAAC;QAGA,GAAW,CAAC,KAAK,GAAG;YACnB,OAAO,EAAE,YAAY;YACrB,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,YAAY;SACpB,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,qBAAqB,yBAuChC;AAKK,MAAM,YAAY,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACnG,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,IAAI,GAAG,MAAM,IAAA,aAAE,EAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAErE,IAAI,IAAI,EAAE,CAAC;gBACT,GAAG,CAAC,IAAI,GAAG;oBACT,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,SAAS,EAAE,IAAI,CAAC,UAAU;oBAC1B,QAAQ,EAAE,IAAI,CAAC,SAAS;oBACxB,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;oBACxC,UAAU,EAAE,IAAI,CAAC,WAAW;iBAC7B,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;QAEjB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAhCW,QAAA,YAAY,gBAgCvB"}