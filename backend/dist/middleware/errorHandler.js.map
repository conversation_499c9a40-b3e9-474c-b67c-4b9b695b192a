{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AAQzC,MAAa,WAAY,SAAQ,KAAK;IAKpC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI,EAAE,IAAa;QACjG,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAbD,kCAaC;AAEM,MAAM,WAAW,GAAG,CAAC,OAAe,EAAE,aAAqB,GAAG,EAAE,IAAa,EAAe,EAAE;IACnG,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1D,CAAC,CAAC;AAFW,QAAA,WAAW,eAEtB;AAEK,MAAM,YAAY,GAAG,CAC1B,KAAe,EACf,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,EAAE,UAAU,GAAG,GAAG,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IAG1C,eAAM,CAAC,KAAK,CAAC,SAAS,UAAU,KAAK,OAAO,EAAE,EAAE;QAC9C,KAAK,EAAE;YACL,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,IAAI,EAAE,KAAK,CAAC,IAAI;SACjB;QACD,OAAO,EAAE;YACP,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC;KACF,CAAC,CAAC;IAGH,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,kBAAkB,CAAC;IAC/B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACpF,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,cAAc,CAAC;IAC3B,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACtC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,mBAAmB,CAAC;IAChC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAClC,UAAU,GAAG,GAAG,CAAC;QACjB,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QACxC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;YACrC,OAAO,GAAG,gBAAgB,CAAC;QAC7B,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;YAC7C,OAAO,GAAG,gBAAgB,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,mBAAmB,CAAC;QAChC,CAAC;IACH,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;QAChE,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,OAAO;YACP,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI;gBAC5C,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;aACjB,CAAC;SACH;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC,CAAC;AACL,CAAC,CAAC;AAhEW,QAAA,YAAY,gBAgEvB;AAEK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAEK,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC1E,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,eAAe,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC;AAHW,QAAA,QAAQ,YAGnB;AAGK,MAAM,eAAe,GAAG,CAAC,OAAe,EAAE,KAAc,EAAE,EAAE;IACjE,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;IACtE,IAAI,KAAK,EAAE,CAAC;QACT,KAAa,CAAC,KAAK,GAAG,KAAK,CAAC;IAC/B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AANW,QAAA,eAAe,mBAM1B;AAGK,MAAM,iBAAiB,GAAG,CAAC,UAAkB,cAAc,EAAE,EAAE;IACpE,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AAC7D,CAAC,CAAC;AAFW,QAAA,iBAAiB,qBAE5B;AAEK,MAAM,cAAc,GAAG,CAAC,UAAkB,WAAW,EAAE,EAAE;IAC9D,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AAC1D,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAGK,MAAM,aAAa,GAAG,CAAC,OAAe,EAAE,EAAE;IAC/C,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;AACzD,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB;AAEK,MAAM,oBAAoB,GAAG,CAAC,UAAkB,mBAAmB,EAAE,EAAE;IAC5E,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;AAClE,CAAC,CAAC;AAFW,QAAA,oBAAoB,wBAE/B;AAEK,MAAM,uBAAuB,GAAG,CAAC,UAAkB,iCAAiC,EAAE,EAAE;IAC7F,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,qBAAqB,CAAC,CAAC;AACpE,CAAC,CAAC;AAFW,QAAA,uBAAuB,2BAElC"}