"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.checkConversionLimits = exports.requireSubscription = exports.requireVerified = exports.authenticate = void 0;
const AuthService_1 = require("../services/AuthService");
const errorHandler_1 = require("./errorHandler");
const database_1 = require("../config/database");
const authService = new AuthService_1.AuthService();
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new errorHandler_1.CustomError('Access token required', 401, true, 'NO_TOKEN');
        }
        const token = authHeader.substring(7);
        const decoded = authService.verifyAccessToken(token);
        const user = await (0, database_1.db)('users').where({ id: decoded.userId }).first();
        if (!user) {
            throw new errorHandler_1.CustomError('User not found', 401, true, 'USER_NOT_FOUND');
        }
        req.user = {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            subscriptionPlan: user.subscription_plan,
            isVerified: user.is_verified
        };
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authenticate = authenticate;
const requireVerified = (req, res, next) => {
    if (!req.user) {
        throw new errorHandler_1.CustomError('Authentication required', 401, true, 'NOT_AUTHENTICATED');
    }
    if (!req.user.isVerified) {
        throw new errorHandler_1.CustomError('Email verification required', 403, true, 'EMAIL_NOT_VERIFIED');
    }
    next();
};
exports.requireVerified = requireVerified;
const requireSubscription = (allowedPlans) => {
    return (req, res, next) => {
        if (!req.user) {
            throw new errorHandler_1.CustomError('Authentication required', 401, true, 'NOT_AUTHENTICATED');
        }
        if (!allowedPlans.includes(req.user.subscriptionPlan)) {
            throw new errorHandler_1.CustomError('Subscription upgrade required', 403, true, 'SUBSCRIPTION_REQUIRED');
        }
        next();
    };
};
exports.requireSubscription = requireSubscription;
const checkConversionLimits = async (req, res, next) => {
    try {
        if (!req.user) {
            throw new errorHandler_1.CustomError('Authentication required', 401, true, 'NOT_AUTHENTICATED');
        }
        const currentMonth = new Date().toISOString().slice(0, 7);
        const usage = await (0, database_1.db)('usage_tracking')
            .where({ user_id: req.user.id, month: currentMonth })
            .first();
        const currentUsage = usage ? usage.conversions_count : 0;
        const limits = {
            free: 3,
            pro: Infinity,
            business: Infinity
        };
        const userLimit = limits[req.user.subscriptionPlan] || 0;
        if (currentUsage >= userLimit) {
            throw new errorHandler_1.CustomError('Conversion limit exceeded for your plan', 429, true, 'LIMIT_EXCEEDED');
        }
        req.usage = {
            current: currentUsage,
            limit: userLimit,
            month: currentMonth
        };
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.checkConversionLimits = checkConversionLimits;
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next();
        }
        const token = authHeader.substring(7);
        try {
            const decoded = authService.verifyAccessToken(token);
            const user = await (0, database_1.db)('users').where({ id: decoded.userId }).first();
            if (user) {
                req.user = {
                    id: user.id,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    subscriptionPlan: user.subscription_plan,
                    isVerified: user.is_verified
                };
            }
        }
        catch (error) {
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map