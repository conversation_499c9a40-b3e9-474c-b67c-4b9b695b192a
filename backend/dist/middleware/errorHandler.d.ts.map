{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAG1D,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,qBAAa,WAAY,SAAQ,KAAM,YAAW,QAAQ;IACxD,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;gBAEF,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,aAAa,GAAE,OAAc,EAAE,IAAI,CAAC,EAAE,MAAM;CAQpG;AAED,eAAO,MAAM,WAAW,GAAI,SAAS,MAAM,EAAE,aAAY,MAAY,EAAE,OAAO,MAAM,KAAG,WAEtF,CAAC;AAEF,eAAO,MAAM,YAAY,GACvB,OAAO,QAAQ,EACf,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IA2DF,CAAC;AAEF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAEF,eAAO,MAAM,QAAQ,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGvE,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,SAAS,MAAM,EAAE,QAAQ,MAAM,gBAM9D,CAAC;AAGF,eAAO,MAAM,iBAAiB,GAAI,UAAS,MAAuB,gBAEjE,CAAC;AAEF,eAAO,MAAM,cAAc,GAAI,UAAS,MAAoB,gBAE3D,CAAC;AAGF,eAAO,MAAM,aAAa,GAAI,SAAS,MAAM,gBAE5C,CAAC;AAEF,eAAO,MAAM,oBAAoB,GAAI,UAAS,MAA4B,gBAEzE,CAAC;AAEF,eAAO,MAAM,uBAAuB,GAAI,UAAS,MAA0C,gBAE1F,CAAC"}