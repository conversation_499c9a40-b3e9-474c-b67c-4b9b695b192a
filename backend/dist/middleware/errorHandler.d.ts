import { Request, Response, NextFunction } from 'express';
export interface AppError extends Error {
    statusCode?: number;
    isOperational?: boolean;
    code?: string;
}
export declare class CustomError extends Error implements AppError {
    statusCode: number;
    isOperational: boolean;
    code?: string;
    constructor(message: string, statusCode?: number, isOperational?: boolean, code?: string);
}
export declare const createError: (message: string, statusCode?: number, code?: string) => CustomError;
export declare const errorHandler: (error: AppError, req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const notFound: (req: Request, res: Response, next: NextFunction) => void;
export declare const validationError: (message: string, field?: string) => CustomError;
export declare const unauthorizedError: (message?: string) => CustomError;
export declare const forbiddenError: (message?: string) => CustomError;
export declare const conflictError: (message: string) => CustomError;
export declare const tooManyRequestsError: (message?: string) => CustomError;
export declare const serviceUnavailableError: (message?: string) => CustomError;
//# sourceMappingURL=errorHandler.d.ts.map