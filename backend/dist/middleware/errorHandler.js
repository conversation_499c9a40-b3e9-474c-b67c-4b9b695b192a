"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceUnavailableError = exports.tooManyRequestsError = exports.conflictError = exports.forbiddenError = exports.unauthorizedError = exports.validationError = exports.notFound = exports.asyncHandler = exports.errorHandler = exports.createError = exports.CustomError = void 0;
const logger_1 = require("../utils/logger");
class CustomError extends Error {
    constructor(message, statusCode = 500, isOperational = true, code) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.code = code;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.CustomError = CustomError;
const createError = (message, statusCode = 500, code) => {
    return new CustomError(message, statusCode, true, code);
};
exports.createError = createError;
const errorHandler = (error, req, res, next) => {
    let { statusCode = 500, message } = error;
    logger_1.logger.error(`Error ${statusCode}: ${message}`, {
        error: {
            message: error.message,
            stack: error.stack,
            code: error.code
        },
        request: {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        }
    });
    if (error.name === 'ValidationError') {
        statusCode = 400;
        message = 'Validation Error';
    }
    else if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = 'Unauthorized';
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        message = 'Invalid ID format';
    }
    else if (error.code === '11000') {
        statusCode = 409;
        message = 'Duplicate field value';
    }
    else if (error.name === 'MulterError') {
        statusCode = 400;
        if (error.code === 'LIMIT_FILE_SIZE') {
            message = 'File too large';
        }
        else if (error.code === 'LIMIT_FILE_COUNT') {
            message = 'Too many files';
        }
        else {
            message = 'File upload error';
        }
    }
    if (process.env.NODE_ENV === 'production' && statusCode === 500) {
        message = 'Internal Server Error';
    }
    res.status(statusCode).json({
        success: false,
        error: {
            message,
            ...(process.env.NODE_ENV === 'development' && {
                stack: error.stack,
                code: error.code
            })
        },
        timestamp: new Date().toISOString(),
        path: req.path
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const notFound = (req, res, next) => {
    const error = new CustomError(`Not found - ${req.originalUrl}`, 404);
    next(error);
};
exports.notFound = notFound;
const validationError = (message, field) => {
    const error = new CustomError(message, 400, true, 'VALIDATION_ERROR');
    if (field) {
        error.field = field;
    }
    return error;
};
exports.validationError = validationError;
const unauthorizedError = (message = 'Unauthorized') => {
    return new CustomError(message, 401, true, 'UNAUTHORIZED');
};
exports.unauthorizedError = unauthorizedError;
const forbiddenError = (message = 'Forbidden') => {
    return new CustomError(message, 403, true, 'FORBIDDEN');
};
exports.forbiddenError = forbiddenError;
const conflictError = (message) => {
    return new CustomError(message, 409, true, 'CONFLICT');
};
exports.conflictError = conflictError;
const tooManyRequestsError = (message = 'Too many requests') => {
    return new CustomError(message, 429, true, 'TOO_MANY_REQUESTS');
};
exports.tooManyRequestsError = tooManyRequestsError;
const serviceUnavailableError = (message = 'Service temporarily unavailable') => {
    return new CustomError(message, 503, true, 'SERVICE_UNAVAILABLE');
};
exports.serviceUnavailableError = serviceUnavailableError;
//# sourceMappingURL=errorHandler.js.map