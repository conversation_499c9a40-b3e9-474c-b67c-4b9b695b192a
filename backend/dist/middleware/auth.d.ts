import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                firstName?: string;
                lastName?: string;
                subscriptionPlan: string;
                isVerified: boolean;
            };
        }
    }
}
export declare const authenticate: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const requireVerified: (req: Request, res: Response, next: NextFunction) => void;
export declare const requireSubscription: (allowedPlans: string[]) => (req: Request, res: Response, next: NextFunction) => void;
export declare const checkConversionLimits: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const optionalAuth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=auth.d.ts.map