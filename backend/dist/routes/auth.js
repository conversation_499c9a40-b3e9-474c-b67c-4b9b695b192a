"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const errorHandler_1 = require("../middleware/errorHandler");
const AuthService_1 = require("../services/AuthService");
const logger_1 = require("../utils/logger");
const router = express_1.default.Router();
const authService = new AuthService_1.AuthService();
const registerValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number and one special character'),
    (0, express_validator_1.body)('firstName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters')
];
const loginValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('Password is required')
];
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
        return;
    }
    next();
};
router.post('/register', registerValidation, handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, firstName, lastName } = req.body;
    logger_1.logger.info(`Registration attempt for email: ${email}`);
    const result = await authService.register({
        email,
        password,
        firstName,
        lastName
    });
    res.status(201).json({
        success: true,
        message: 'User registered successfully. Please check your email for verification.',
        data: {
            user: result.user,
            tokens: result.tokens
        }
    });
}));
router.post('/login', loginValidation, handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    logger_1.logger.info(`Login attempt for email: ${email}`);
    const result = await authService.login(email, password);
    res.json({
        success: true,
        message: 'Login successful',
        data: {
            user: result.user,
            tokens: result.tokens
        }
    });
}));
router.post('/refresh', (0, express_validator_1.body)('refreshToken').notEmpty().withMessage('Refresh token is required'), handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    const result = await authService.refreshToken(refreshToken);
    res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
            tokens: result.tokens
        }
    });
}));
router.post('/logout', (0, express_validator_1.body)('refreshToken').notEmpty().withMessage('Refresh token is required'), handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    await authService.logout(refreshToken);
    res.json({
        success: true,
        message: 'Logout successful'
    });
}));
router.post('/verify-email', (0, express_validator_1.body)('token').notEmpty().withMessage('Verification token is required'), handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token } = req.body;
    await authService.verifyEmail(token);
    res.json({
        success: true,
        message: 'Email verified successfully'
    });
}));
router.post('/forgot-password', (0, express_validator_1.body)('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'), handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email } = req.body;
    await authService.forgotPassword(email);
    res.json({
        success: true,
        message: 'Password reset email sent'
    });
}));
router.post('/reset-password', [
    (0, express_validator_1.body)('token').notEmpty().withMessage('Reset token is required'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number and one special character')
], handleValidationErrors, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { token, password } = req.body;
    await authService.resetPassword(token, password);
    res.json({
        success: true,
        message: 'Password reset successfully'
    });
}));
module.exports = router;
//# sourceMappingURL=auth.js.map