"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const UserService_1 = require("../services/UserService");
const router = express_1.default.Router();
const userService = new UserService_1.UserService();
router.use(auth_1.authenticate);
router.get('/profile', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await userService.getProfile(req.user.id);
    res.json({
        success: true,
        data: { user }
    });
}));
router.put('/profile', [
    (0, express_validator_1.body)('firstName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email')
], (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
        return;
    }
    const { firstName, lastName, email } = req.body;
    const user = await userService.updateProfile(req.user.id, {
        firstName,
        lastName,
        email
    });
    res.json({
        success: true,
        message: 'Profile updated successfully',
        data: { user }
    });
}));
router.get('/usage', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const usage = await userService.getUsageStats(req.user.id);
    res.json({
        success: true,
        data: { usage }
    });
}));
router.get('/subscription', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const subscription = await userService.getSubscription(req.user.id);
    res.json({
        success: true,
        data: { subscription }
    });
}));
router.delete('/account', auth_1.requireVerified, (0, express_validator_1.body)('password').notEmpty().withMessage('Password is required for account deletion'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
        return;
    }
    const { password } = req.body;
    await userService.deleteAccount(req.user.id, password);
    res.json({
        success: true,
        message: 'Account deleted successfully'
    });
}));
module.exports = router;
//# sourceMappingURL=users.js.map