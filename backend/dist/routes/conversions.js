"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const ConversionService_1 = require("../services/ConversionService");
const errorHandler_2 = require("../middleware/errorHandler");
const router = express_1.default.Router();
const conversionService = new ConversionService_1.ConversionService();
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, process.env.UPLOAD_PATH || './uploads');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path_1.default.extname(file.originalname));
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800'),
        files: 1
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/pdf') {
            cb(null, true);
        }
        else {
            cb(new errorHandler_2.CustomError('Only PDF files are allowed', 400, true, 'INVALID_FILE_TYPE'));
        }
    }
});
router.use(auth_1.authenticate);
router.use(auth_1.requireVerified);
router.post('/upload', auth_1.checkConversionLimits, upload.single('pdf'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        throw new errorHandler_2.CustomError('No file uploaded', 400, true, 'NO_FILE');
    }
    const conversion = await conversionService.startConversion({
        userId: req.user.id,
        file: req.file,
        options: req.body.options ? JSON.parse(req.body.options) : {}
    });
    res.status(202).json({
        success: true,
        message: 'Conversion started',
        data: { conversion }
    });
}));
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;
    const conversions = await conversionService.getConversions(req.user.id, {
        page,
        limit,
        status
    });
    res.json({
        success: true,
        data: conversions
    });
}));
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const conversion = await conversionService.getConversion(req.params.id, req.user.id);
    res.json({
        success: true,
        data: { conversion }
    });
}));
router.get('/:id/download', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const filePath = await conversionService.getDownloadPath(req.params.id, req.user.id);
    res.download(filePath, (err) => {
        if (err) {
            throw new errorHandler_2.CustomError('File not found or download failed', 404, true, 'DOWNLOAD_FAILED');
        }
    });
}));
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    await conversionService.deleteConversion(req.params.id, req.user.id);
    res.json({
        success: true,
        message: 'Conversion deleted successfully'
    });
}));
router.post('/:id/retry', auth_1.checkConversionLimits, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const conversion = await conversionService.retryConversion(req.params.id, req.user.id);
    res.json({
        success: true,
        message: 'Conversion retry started',
        data: { conversion }
    });
}));
module.exports = router;
//# sourceMappingURL=conversions.js.map