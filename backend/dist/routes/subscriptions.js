"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const router = express_1.default.Router();
router.use(auth_1.authenticate);
router.use(auth_1.requireVerified);
router.get('/plans', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const plans = [
        {
            id: 'free',
            name: 'Free',
            price: 0,
            currency: 'PLN',
            interval: 'month',
            features: [
                '3 konwersje miesięcznie',
                'Pliki do 50 MB',
                'Podstawowe szablony',
                'Eksport do Excel'
            ],
            limits: {
                conversions: 3,
                fileSize: 50 * 1024 * 1024
            }
        },
        {
            id: 'pro',
            name: 'Pro',
            price: 19,
            currency: 'PLN',
            interval: 'month',
            features: [
                'Nielimitowane konwersje',
                'Pliki do 200 MB',
                'Zaawansowane AI i autokorekta',
                'Wszystkie szablony',
                'Eksport do CSV i Google Sheets',
                'Priorytetowa obsługa'
            ],
            limits: {
                conversions: Infinity,
                fileSize: 200 * 1024 * 1024
            },
            stripeProductId: process.env.STRIPE_PRO_PRODUCT_ID,
            stripePriceId: process.env.STRIPE_PRO_PRICE_ID
        },
        {
            id: 'business',
            name: 'Business',
            price: 49,
            currency: 'PLN',
            interval: 'month',
            features: [
                'Wszystko z Pro',
                'API dostęp',
                'Masowa konwersja plików',
                'Zaawansowana analityka',
                'Dedykowany menedżer konta',
                'SLA 99.9%'
            ],
            limits: {
                conversions: Infinity,
                fileSize: 500 * 1024 * 1024
            },
            stripeProductId: process.env.STRIPE_BUSINESS_PRODUCT_ID,
            stripePriceId: process.env.STRIPE_BUSINESS_PRICE_ID
        }
    ];
    res.json({
        success: true,
        data: { plans }
    });
}));
router.get('/current', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        data: {
            subscription: {
                plan: req.user.subscriptionPlan,
                status: 'active',
                currentPeriodEnd: null,
                cancelAtPeriodEnd: false
            }
        }
    });
}));
router.post('/create-checkout-session', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Stripe integration not yet implemented'
    });
}));
router.post('/cancel', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Stripe integration not yet implemented'
    });
}));
router.post('/reactivate', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Stripe integration not yet implemented'
    });
}));
module.exports = router;
//# sourceMappingURL=subscriptions.js.map