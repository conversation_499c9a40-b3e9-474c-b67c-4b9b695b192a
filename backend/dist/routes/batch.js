"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const auth_1 = require("../middleware/auth");
const BatchProcessingService_1 = require("../services/BatchProcessingService");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
const express_validator_1 = require("express-validator");
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array()
        });
        return;
    }
    next();
};
const router = express_1.default.Router();
const batchService = new BatchProcessingService_1.BatchProcessingService();
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, process.env.UPLOAD_DIR || 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, `batch-${uniqueSuffix}-${file.originalname}`);
    }
});
const upload = (0, multer_1.default)({
    storage,
    limits: {
        fileSize: 50 * 1024 * 1024,
        files: 50
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/pdf') {
            cb(null, true);
        }
        else {
            cb(new errorHandler_1.CustomError('Only PDF files are allowed', 400, true, 'INVALID_FILE_TYPE'));
        }
    }
});
router.post('/create', auth_1.authenticate, upload.array('files', 50), [
    (0, express_validator_1.body)('template').optional().isString().isLength({ max: 100 }),
    (0, express_validator_1.body)('outputFormat').optional().isIn(['xlsx', 'csv']),
    (0, express_validator_1.body)('extractTables').optional().isBoolean(),
    (0, express_validator_1.body)('extractText').optional().isBoolean(),
    (0, express_validator_1.body)('ocrEnabled').optional().isBoolean(),
    (0, express_validator_1.body)('maxConcurrent').optional().isInt({ min: 1, max: 5 }),
    (0, express_validator_1.body)('priority').optional().isIn(['low', 'normal', 'high'])
], validateRequest, async (req, res, next) => {
    try {
        const files = req.files;
        if (!files || files.length === 0) {
            throw new errorHandler_1.CustomError('No files uploaded', 400, true, 'NO_FILES');
        }
        const processingOptions = {
            template: req.body.template,
            outputFormat: req.body.outputFormat || 'xlsx',
            extractTables: req.body.extractTables !== false,
            extractText: req.body.extractText !== false,
            ocrEnabled: req.body.ocrEnabled || false
        };
        const batchOptions = {
            maxConcurrent: parseInt(req.body.maxConcurrent) || 2,
            priority: req.body.priority || 'normal',
            notifyOnComplete: req.body.notifyOnComplete || false,
            retryFailures: req.body.retryFailures || false,
            maxRetries: parseInt(req.body.maxRetries) || 1
        };
        const batchJob = await batchService.createBatchJob(req.user.id, files, processingOptions, batchOptions);
        logger_1.logger.info(`Batch job created: ${batchJob.id} by user ${req.user.id}`);
        res.status(201).json({
            success: true,
            data: {
                jobId: batchJob.id,
                status: batchJob.status,
                totalFiles: batchJob.progress.total,
                message: 'Batch job created successfully'
            }
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/:jobId', auth_1.authenticate, [
    (0, express_validator_1.param)('jobId').isUUID().withMessage('Invalid job ID format')
], validateRequest, async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const batchJob = await batchService.getBatchJob(jobId, req.user.id);
        if (!batchJob) {
            throw new errorHandler_1.CustomError('Batch job not found', 404, true, 'JOB_NOT_FOUND');
        }
        res.json({
            success: true,
            data: batchJob
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/', auth_1.authenticate, [
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
    (0, express_validator_1.query)('status').optional().isIn(['pending', 'processing', 'completed', 'failed', 'cancelled'])
], validateRequest, async (req, res, next) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const result = await batchService.getUserBatchJobs(req.user.id, page, limit);
        let filteredJobs = result.jobs;
        if (status) {
            filteredJobs = result.jobs.filter(job => job.status === status);
        }
        res.json({
            success: true,
            data: {
                jobs: filteredJobs,
                pagination: {
                    page,
                    limit,
                    total: result.total,
                    pages: Math.ceil(result.total / limit)
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
});
router.post('/:jobId/cancel', auth_1.authenticate, [
    (0, express_validator_1.param)('jobId').isUUID().withMessage('Invalid job ID format')
], validateRequest, async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const cancelled = await batchService.cancelBatchJob(jobId, req.user.id);
        if (!cancelled) {
            throw new errorHandler_1.CustomError('Failed to cancel batch job', 400, true, 'CANCEL_FAILED');
        }
        logger_1.logger.info(`Batch job cancelled: ${jobId} by user ${req.user.id}`);
        res.json({
            success: true,
            message: 'Batch job cancelled successfully'
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/:jobId/download', auth_1.authenticate, [
    (0, express_validator_1.param)('jobId').isUUID().withMessage('Invalid job ID format')
], validateRequest, async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const batchJob = await batchService.getBatchJob(jobId, req.user.id);
        if (!batchJob) {
            throw new errorHandler_1.CustomError('Batch job not found', 404, true, 'JOB_NOT_FOUND');
        }
        if (batchJob.status !== 'completed') {
            throw new errorHandler_1.CustomError('Batch job not completed yet', 400, true, 'JOB_NOT_COMPLETED');
        }
        if (!batchJob.results || batchJob.results.length === 0) {
            throw new errorHandler_1.CustomError('No results available for download', 404, true, 'NO_RESULTS');
        }
        const downloadableFiles = batchJob.results
            .filter(result => result.success && result.outputPath)
            .map(result => ({
            fileName: result.fileName.replace('.pdf', '.xlsx'),
            downloadUrl: `/api/conversions/download/${path_1.default.basename(result.outputPath)}`
        }));
        res.json({
            success: true,
            data: {
                jobId,
                totalFiles: downloadableFiles.length,
                files: downloadableFiles,
                message: 'Batch results ready for download'
            }
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/stats/summary', auth_1.authenticate, async (req, res, next) => {
    try {
        const userId = req.user.id;
        const stats = {
            totalBatchJobs: 0,
            completedJobs: 0,
            failedJobs: 0,
            totalFilesProcessed: 0,
            averageProcessingTime: 0,
            successRate: 0
        };
        res.json({
            success: true,
            data: stats
        });
    }
    catch (error) {
        next(error);
    }
});
router.use((error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                error: 'File too large. Maximum size is 50MB per file.',
                code: 'FILE_TOO_LARGE'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                error: 'Too many files. Maximum 50 files per batch.',
                code: 'TOO_MANY_FILES'
            });
        }
    }
    return next(error);
});
module.exports = router;
//# sourceMappingURL=batch.js.map