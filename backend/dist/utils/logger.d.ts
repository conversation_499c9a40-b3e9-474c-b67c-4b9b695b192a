import winston from 'winston';
export declare const logger: winston.Logger;
export declare const morganStream: {
    write: (message: string) => void;
};
export declare const logError: (error: Error, context?: any) => void;
export declare const logInfo: (message: string, context?: any) => void;
export declare const logWarning: (message: string, context?: any) => void;
export declare const logDebug: (message: string, context?: any) => void;
//# sourceMappingURL=logger.d.ts.map