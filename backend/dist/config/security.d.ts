export interface SecurityConfig {
    encryption: {
        algorithm: string;
        keyLength: number;
        ivLength: number;
        tagLength: number;
    };
    csp: {
        defaultSrc: string[];
        scriptSrc: string[];
        styleSrc: string[];
        imgSrc: string[];
        connectSrc: string[];
        fontSrc: string[];
        objectSrc: string[];
        mediaSrc: string[];
        frameSrc: string[];
        workerSrc: string[];
        childSrc: string[];
    };
    rateLimit: {
        windowMs: number;
        max: number;
    };
    jwt: {
        accessTokenExpiry: string;
        refreshTokenExpiry: string;
    };
}
export declare const securityConfig: SecurityConfig;
export declare class EncryptionService {
    private static readonly ENCRYPTION_KEY;
    static encrypt(text: string): {
        encrypted: string;
        iv: string;
        tag: string;
    };
    static decrypt(encryptedData: {
        encrypted: string;
        iv: string;
        tag: string;
    }): string;
    static hashPassword(password: string): Promise<string>;
    static comparePassword(password: string, hash: string): Promise<boolean>;
    static generateSecureToken(): string;
}
//# sourceMappingURL=security.d.ts.map