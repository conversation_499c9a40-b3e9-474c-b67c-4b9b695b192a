"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptionService = exports.securityConfig = void 0;
const crypto_1 = __importDefault(require("crypto"));
exports.securityConfig = {
    encryption: {
        algorithm: 'aes-256-gcm',
        keyLength: 32,
        ivLength: 16,
        tagLength: 16
    },
    csp: {
        defaultSrc: ["'self'"],
        scriptSrc: [
            "'self'",
            "'wasm-unsafe-eval'",
            "'unsafe-inline'"
        ],
        styleSrc: [
            "'self'",
            "'unsafe-inline'",
            "https://fonts.googleapis.com"
        ],
        imgSrc: [
            "'self'",
            "data:",
            "https:"
        ],
        connectSrc: [
            "'self'",
            "https://api.stripe.com",
            "wss://localhost:*"
        ],
        fontSrc: [
            "'self'",
            "https://fonts.gstatic.com"
        ],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: [
            "'self'",
            "https://js.stripe.com"
        ],
        workerSrc: [
            "'self'",
            "blob:"
        ],
        childSrc: [
            "'self'",
            "blob:"
        ]
    },
    rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: 100
    },
    jwt: {
        accessTokenExpiry: '15m',
        refreshTokenExpiry: '7d'
    }
};
class EncryptionService {
    static encrypt(text) {
        const iv = crypto_1.default.randomBytes(exports.securityConfig.encryption.ivLength);
        const cipher = crypto_1.default.createCipher(exports.securityConfig.encryption.algorithm, EncryptionService.ENCRYPTION_KEY);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return {
            encrypted,
            iv: iv.toString('hex'),
            tag: 'mock-tag'
        };
    }
    static decrypt(encryptedData) {
        const decipher = crypto_1.default.createDecipher(exports.securityConfig.encryption.algorithm, EncryptionService.ENCRYPTION_KEY);
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
    static hashPassword(password) {
        const bcrypt = require('bcryptjs');
        return bcrypt.hash(password, 12);
    }
    static comparePassword(password, hash) {
        const bcrypt = require('bcryptjs');
        return bcrypt.compare(password, hash);
    }
    static generateSecureToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
}
exports.EncryptionService = EncryptionService;
EncryptionService.ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || crypto_1.default.randomBytes(32);
//# sourceMappingURL=security.js.map