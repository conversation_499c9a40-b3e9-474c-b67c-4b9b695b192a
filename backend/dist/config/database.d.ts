import knex from 'knex';
export interface DatabaseConfig {
    client: string;
    connection: {
        host: string;
        port: number;
        user: string;
        password: string;
        database: string;
        ssl?: boolean | object;
    };
    pool: {
        min: number;
        max: number;
        acquireTimeoutMillis: number;
        createTimeoutMillis: number;
        destroyTimeoutMillis: number;
        idleTimeoutMillis: number;
        reapIntervalMillis: number;
        createRetryIntervalMillis: number;
    };
    migrations: {
        directory: string;
        tableName: string;
    };
    seeds: {
        directory: string;
    };
}
export declare const dbConfig: DatabaseConfig;
export declare const db: knex.Knex<any, unknown[]>;
export declare const testConnection: () => Promise<boolean>;
export declare const closeConnection: () => Promise<void>;
export interface User {
    id: string;
    email: string;
    password_hash: string;
    first_name?: string;
    last_name?: string;
    subscription_plan: 'free' | 'pro' | 'business';
    is_verified: boolean;
    verification_token?: string;
    reset_token?: string;
    reset_token_expires?: Date;
    created_at: Date;
    updated_at: Date;
}
export interface Conversion {
    id: string;
    user_id: string;
    file_name: string;
    file_size: number;
    file_path: string;
    processing_time?: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    error_message?: string;
    result_path?: string;
    complexity_score?: number;
    ai_model_used?: string;
    created_at: Date;
    updated_at: Date;
}
export interface Subscription {
    id: string;
    user_id: string;
    stripe_subscription_id?: string;
    stripe_customer_id?: string;
    plan: 'free' | 'pro' | 'business';
    status: 'active' | 'canceled' | 'past_due' | 'unpaid';
    current_period_start: Date;
    current_period_end: Date;
    cancel_at_period_end: boolean;
    created_at: Date;
    updated_at: Date;
}
export interface UsageTracking {
    id: string;
    user_id: string;
    month: string;
    conversions_count: number;
    total_file_size: number;
    total_processing_time: number;
    created_at: Date;
    updated_at: Date;
}
//# sourceMappingURL=database.d.ts.map