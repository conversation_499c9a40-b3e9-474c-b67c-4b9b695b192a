"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeConnection = exports.testConnection = exports.db = exports.dbConfig = void 0;
const knex_1 = __importDefault(require("knex"));
const logger_1 = require("../utils/logger");
exports.dbConfig = {
    client: 'postgresql',
    connection: {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || 'password',
        database: process.env.DB_NAME || 'pdfexcel',
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    },
    pool: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 60000,
        createTimeoutMillis: 30000,
        destroyTimeoutMillis: 5000,
        idleTimeoutMillis: 30000,
        reapIntervalMillis: 1000,
        createRetryIntervalMillis: 100
    },
    migrations: {
        directory: './migrations',
        tableName: 'knex_migrations'
    },
    seeds: {
        directory: './seeds'
    }
};
exports.db = (0, knex_1.default)(exports.dbConfig);
const testConnection = async () => {
    try {
        await exports.db.raw('SELECT 1');
        logger_1.logger.info('Database connection established successfully');
        return true;
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to database:', error);
        return false;
    }
};
exports.testConnection = testConnection;
const closeConnection = async () => {
    try {
        await exports.db.destroy();
        logger_1.logger.info('Database connection closed');
    }
    catch (error) {
        logger_1.logger.error('Error closing database connection:', error);
    }
};
exports.closeConnection = closeConnection;
//# sourceMappingURL=database.js.map