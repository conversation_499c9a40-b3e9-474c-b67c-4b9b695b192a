"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchProcessingService = void 0;
const uuid_1 = require("uuid");
const database_1 = require("../config/database");
const logger_1 = require("../utils/logger");
const PDFProcessingService_1 = require("./PDFProcessingService");
const errorHandler_1 = require("../middleware/errorHandler");
class BatchProcessingService {
    constructor() {
        this.activeJobs = new Map();
        this.maxConcurrentJobs = 3;
        this.maxConcurrentFiles = 2;
        this.pdfProcessor = new PDFProcessingService_1.PDFProcessingService();
    }
    async createBatchJob(userId, files, options, batchOptions = {}) {
        try {
            const jobId = (0, uuid_1.v4)();
            if (!files || files.length === 0) {
                throw new errorHandler_1.CustomError('No files provided for batch processing', 400, true, 'NO_FILES');
            }
            if (files.length > 50) {
                throw new errorHandler_1.CustomError('Maximum 50 files allowed per batch', 400, true, 'TOO_MANY_FILES');
            }
            const batchFiles = files.map(file => ({
                id: (0, uuid_1.v4)(),
                originalName: file.originalname,
                filePath: file.path,
                fileSize: file.size,
                status: 'pending'
            }));
            const batchJob = {
                id: jobId,
                userId,
                name: `Batch Job ${new Date().toISOString().slice(0, 19)}`,
                files: batchFiles,
                options,
                status: 'pending',
                progress: {
                    total: batchFiles.length,
                    completed: 0,
                    failed: 0
                },
                createdAt: new Date()
            };
            await this.saveBatchJobToDatabase(batchJob);
            this.activeJobs.set(jobId, batchJob);
            logger_1.logger.info(`Batch job created: ${jobId} with ${files.length} files`);
            this.processBatchJobAsync(jobId, batchOptions);
            return batchJob;
        }
        catch (error) {
            logger_1.logger.error('Failed to create batch job:', error);
            throw error;
        }
    }
    async getBatchJob(jobId, userId) {
        try {
            const activeJob = this.activeJobs.get(jobId);
            if (activeJob && activeJob.userId === userId) {
                return activeJob;
            }
            const job = await this.getBatchJobFromDatabase(jobId, userId);
            return job;
        }
        catch (error) {
            logger_1.logger.error('Failed to get batch job:', error);
            return null;
        }
    }
    async getUserBatchJobs(userId, page = 1, limit = 10) {
        try {
            const offset = (page - 1) * limit;
            const jobs = await (0, database_1.db)('batch_jobs')
                .where({ user_id: userId })
                .orderBy('created_at', 'desc')
                .limit(limit)
                .offset(offset);
            const [{ count }] = await (0, database_1.db)('batch_jobs')
                .where({ user_id: userId })
                .count('* as count');
            const batchJobs = jobs.map(job => this.mapDatabaseToBatchJob(job));
            return {
                jobs: batchJobs,
                total: parseInt(count)
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user batch jobs:', error);
            return { jobs: [], total: 0 };
        }
    }
    async cancelBatchJob(jobId, userId) {
        try {
            const job = this.activeJobs.get(jobId);
            if (!job || job.userId !== userId) {
                throw new errorHandler_1.CustomError('Batch job not found', 404, true, 'JOB_NOT_FOUND');
            }
            if (job.status === 'completed' || job.status === 'failed') {
                throw new errorHandler_1.CustomError('Cannot cancel completed or failed job', 400, true, 'INVALID_STATUS');
            }
            job.status = 'cancelled';
            await (0, database_1.db)('batch_jobs')
                .where({ id: jobId })
                .update({
                status: 'cancelled',
                completed_at: new Date()
            });
            this.activeJobs.delete(jobId);
            logger_1.logger.info(`Batch job cancelled: ${jobId}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to cancel batch job:', error);
            throw error;
        }
    }
    async processBatchJobAsync(jobId, batchOptions = {}) {
        const job = this.activeJobs.get(jobId);
        if (!job) {
            logger_1.logger.error(`Batch job not found: ${jobId}`);
            return;
        }
        try {
            logger_1.logger.info(`Starting batch processing: ${jobId}`);
            job.status = 'processing';
            job.startedAt = new Date();
            await this.updateBatchJobInDatabase(job);
            const maxConcurrent = batchOptions.maxConcurrent || this.maxConcurrentFiles;
            const results = [];
            for (let i = 0; i < job.files.length; i += maxConcurrent) {
                const batch = job.files.slice(i, i + maxConcurrent);
                const batchPromises = batch.map(file => this.processFileInBatch(job, file, batchOptions));
                const batchResults = await Promise.allSettled(batchPromises);
                batchResults.forEach((result, index) => {
                    const file = batch[index];
                    if (result.status === 'fulfilled') {
                        results.push(result.value);
                        job.progress.completed++;
                    }
                    else {
                        logger_1.logger.error(`File processing failed: ${file.originalName}`, result.reason);
                        file.status = 'failed';
                        file.error = result.reason?.message || 'Unknown error';
                        job.progress.failed++;
                        results.push({
                            fileId: file.id,
                            fileName: file.originalName,
                            success: false,
                            processingTime: 0,
                            error: file.error
                        });
                    }
                });
                await this.updateBatchJobInDatabase(job);
                const currentJob = this.activeJobs.get(jobId);
                if (currentJob && currentJob.status === 'cancelled') {
                    break;
                }
            }
            job.status = 'completed';
            job.completedAt = new Date();
            job.results = results;
            await this.updateBatchJobInDatabase(job);
            this.activeJobs.delete(jobId);
            logger_1.logger.info(`Batch job completed: ${jobId} (${job.progress.completed}/${job.progress.total} successful)`);
        }
        catch (error) {
            logger_1.logger.error(`Batch job failed: ${jobId}`, error);
            job.status = 'failed';
            job.error = error instanceof Error ? error.message : 'Unknown error';
            job.completedAt = new Date();
            await this.updateBatchJobInDatabase(job);
            this.activeJobs.delete(jobId);
        }
    }
    async processFileInBatch(job, file, batchOptions) {
        const startTime = Date.now();
        try {
            logger_1.logger.info(`Processing file in batch: ${file.originalName}`);
            file.status = 'processing';
            job.progress.currentFile = file.originalName;
            const isValid = await PDFProcessingService_1.PDFProcessingService.validatePDF(file.filePath);
            if (!isValid) {
                throw new Error('Invalid PDF file');
            }
            const result = await this.pdfProcessor.processPDF(file.filePath, job.options, (progress) => {
                logger_1.logger.debug(`File ${file.originalName} progress: ${progress.stage} - ${progress.progress}%`);
            });
            if (result.success && result.outputPath) {
                file.status = 'completed';
                file.outputPath = result.outputPath;
                file.processingTime = result.processingTime;
                return {
                    fileId: file.id,
                    fileName: file.originalName,
                    success: true,
                    outputPath: result.outputPath,
                    processingTime: result.processingTime,
                    complexity: result.complexity
                };
            }
            else {
                throw new Error(result.error || 'Processing failed');
            }
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            file.status = 'failed';
            file.error = errorMessage;
            file.processingTime = processingTime;
            throw new Error(errorMessage);
        }
    }
    async saveBatchJobToDatabase(job) {
        try {
            await (0, database_1.db)('batch_jobs').insert({
                id: job.id,
                user_id: job.userId,
                name: job.name,
                status: job.status,
                total_files: job.progress.total,
                completed_files: job.progress.completed,
                failed_files: job.progress.failed,
                options: JSON.stringify(job.options),
                files: JSON.stringify(job.files),
                created_at: job.createdAt
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to save batch job to database:', error);
            throw error;
        }
    }
    async updateBatchJobInDatabase(job) {
        try {
            await (0, database_1.db)('batch_jobs')
                .where({ id: job.id })
                .update({
                status: job.status,
                completed_files: job.progress.completed,
                failed_files: job.progress.failed,
                files: JSON.stringify(job.files),
                results: job.results ? JSON.stringify(job.results) : null,
                started_at: job.startedAt,
                completed_at: job.completedAt,
                error_message: job.error
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to update batch job in database:', error);
        }
    }
    async getBatchJobFromDatabase(jobId, userId) {
        try {
            const job = await (0, database_1.db)('batch_jobs')
                .where({ id: jobId, user_id: userId })
                .first();
            return job ? this.mapDatabaseToBatchJob(job) : null;
        }
        catch (error) {
            logger_1.logger.error('Failed to get batch job from database:', error);
            return null;
        }
    }
    mapDatabaseToBatchJob(dbJob) {
        return {
            id: dbJob.id,
            userId: dbJob.user_id,
            name: dbJob.name,
            files: JSON.parse(dbJob.files || '[]'),
            options: JSON.parse(dbJob.options || '{}'),
            status: dbJob.status,
            progress: {
                total: dbJob.total_files,
                completed: dbJob.completed_files,
                failed: dbJob.failed_files
            },
            createdAt: dbJob.created_at,
            startedAt: dbJob.started_at,
            completedAt: dbJob.completed_at,
            results: dbJob.results ? JSON.parse(dbJob.results) : undefined,
            error: dbJob.error_message
        };
    }
}
exports.BatchProcessingService = BatchProcessingService;
//# sourceMappingURL=BatchProcessingService.js.map