"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const jwt = __importStar(require("jsonwebtoken"));
const uuid_1 = require("uuid");
const database_1 = require("../config/database");
const security_1 = require("../config/security");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
class AuthService {
    constructor() {
        this.JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
        this.JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
        this.ACCESS_TOKEN_EXPIRY = process.env.JWT_ACCESS_EXPIRY || '15m';
        this.REFRESH_TOKEN_EXPIRY = process.env.JWT_REFRESH_EXPIRY || '7d';
    }
    async register(data) {
        const { email, password, firstName, lastName } = data;
        const existingUser = await (0, database_1.db)('users').where({ email }).first();
        if (existingUser) {
            throw new errorHandler_1.CustomError('User with this email already exists', 409, true, 'USER_EXISTS');
        }
        const passwordHash = await security_1.EncryptionService.hashPassword(password);
        const verificationToken = security_1.EncryptionService.generateSecureToken();
        const [user] = await (0, database_1.db)('users')
            .insert({
            id: (0, uuid_1.v4)(),
            email,
            password_hash: passwordHash,
            first_name: firstName,
            last_name: lastName,
            verification_token: verificationToken,
            subscription_plan: 'free',
            is_verified: false
        })
            .returning(['id', 'email', 'first_name', 'last_name', 'subscription_plan', 'is_verified']);
        logger_1.logger.info(`User registered successfully: ${email}`);
        const tokens = await this.generateTokens(user.id);
        return {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                subscriptionPlan: user.subscription_plan,
                isVerified: user.is_verified
            },
            tokens
        };
    }
    async login(email, password) {
        const user = await (0, database_1.db)('users').where({ email }).first();
        if (!user) {
            throw new errorHandler_1.CustomError('Invalid credentials', 401, true, 'INVALID_CREDENTIALS');
        }
        const isPasswordValid = await security_1.EncryptionService.comparePassword(password, user.password_hash);
        if (!isPasswordValid) {
            throw new errorHandler_1.CustomError('Invalid credentials', 401, true, 'INVALID_CREDENTIALS');
        }
        logger_1.logger.info(`User logged in successfully: ${email}`);
        const tokens = await this.generateTokens(user.id);
        return {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                subscriptionPlan: user.subscription_plan,
                isVerified: user.is_verified
            },
            tokens
        };
    }
    async refreshToken(refreshToken) {
        try {
            const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET);
            const user = await (0, database_1.db)('users').where({ id: decoded.userId }).first();
            if (!user) {
                throw new errorHandler_1.CustomError('Invalid refresh token', 401, true, 'INVALID_TOKEN');
            }
            const tokens = await this.generateTokens(user.id);
            return { tokens };
        }
        catch (error) {
            throw new errorHandler_1.CustomError('Invalid refresh token', 401, true, 'INVALID_TOKEN');
        }
    }
    async logout(refreshToken) {
        try {
            jwt.verify(refreshToken, this.JWT_REFRESH_SECRET);
        }
        catch (error) {
            throw new errorHandler_1.CustomError('Invalid refresh token', 401, true, 'INVALID_TOKEN');
        }
    }
    async verifyEmail(token) {
        const user = await (0, database_1.db)('users').where({ verification_token: token }).first();
        if (!user) {
            throw new errorHandler_1.CustomError('Invalid verification token', 400, true, 'INVALID_TOKEN');
        }
        await (0, database_1.db)('users')
            .where({ id: user.id })
            .update({
            is_verified: true,
            verification_token: null
        });
        logger_1.logger.info(`Email verified for user: ${user.email}`);
    }
    async forgotPassword(email) {
        const user = await (0, database_1.db)('users').where({ email }).first();
        if (!user) {
            return;
        }
        const resetToken = security_1.EncryptionService.generateSecureToken();
        const resetTokenExpires = new Date(Date.now() + 3600000);
        await (0, database_1.db)('users')
            .where({ id: user.id })
            .update({
            reset_token: resetToken,
            reset_token_expires: resetTokenExpires
        });
        logger_1.logger.info(`Password reset requested for user: ${email}`);
    }
    async resetPassword(token, newPassword) {
        const user = await (0, database_1.db)('users')
            .where({ reset_token: token })
            .where('reset_token_expires', '>', new Date())
            .first();
        if (!user) {
            throw new errorHandler_1.CustomError('Invalid or expired reset token', 400, true, 'INVALID_TOKEN');
        }
        const passwordHash = await security_1.EncryptionService.hashPassword(newPassword);
        await (0, database_1.db)('users')
            .where({ id: user.id })
            .update({
            password_hash: passwordHash,
            reset_token: null,
            reset_token_expires: null
        });
        logger_1.logger.info(`Password reset successfully for user: ${user.email}`);
    }
    async generateTokens(userId) {
        const payload = { userId };
        const accessToken = jwt.sign(payload, this.JWT_SECRET, { expiresIn: this.ACCESS_TOKEN_EXPIRY });
        const refreshToken = jwt.sign(payload, this.JWT_REFRESH_SECRET, { expiresIn: this.REFRESH_TOKEN_EXPIRY });
        return {
            accessToken: accessToken,
            refreshToken: refreshToken
        };
    }
    verifyAccessToken(token) {
        try {
            return jwt.verify(token, this.JWT_SECRET);
        }
        catch (error) {
            throw new errorHandler_1.CustomError('Invalid access token', 401, true, 'INVALID_TOKEN');
        }
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=AuthService.js.map