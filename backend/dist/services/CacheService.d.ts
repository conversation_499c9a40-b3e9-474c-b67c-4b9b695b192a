export interface CacheOptions {
    ttl?: number;
    compress?: boolean;
    namespace?: string;
}
export interface ProcessingCache {
    fileHash: string;
    complexity: any;
    extractedData: any;
    processingTime: number;
    timestamp: number;
    version: string;
}
export declare class CacheService {
    private client;
    private isConnected;
    private readonly defaultTTL;
    private readonly namespace;
    constructor();
    private initializeRedis;
    private generateFileHash;
    private getCacheKey;
    cacheProcessingResult(filePath: string, options: any, result: any, cacheOptions?: CacheOptions): Promise<boolean>;
    getCachedProcessingResult(filePath: string, options: any, namespace?: string): Promise<ProcessingCache | null>;
    cacheComplexityAnalysis(filePath: string, complexity: any, cacheOptions?: CacheOptions): Promise<boolean>;
    getCachedComplexityAnalysis(filePath: string, namespace?: string): Promise<any | null>;
    cacheUserSession(userId: string, sessionData: any, ttl?: number): Promise<boolean>;
    getCachedUserSession(userId: string): Promise<any | null>;
    invalidateCache(pattern: string, namespace?: string): Promise<number>;
    getCacheStats(): Promise<any>;
    clearAllCache(): Promise<boolean>;
    disconnect(): Promise<void>;
}
//# sourceMappingURL=CacheService.d.ts.map