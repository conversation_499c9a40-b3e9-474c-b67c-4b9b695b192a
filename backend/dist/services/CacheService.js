"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const crypto = __importStar(require("crypto"));
const logger_1 = require("../utils/logger");
class CacheService {
    constructor() {
        this.client = null;
        this.isConnected = false;
        this.defaultTTL = 3600;
        this.namespace = 'pdf-processing';
        this.initializeRedis();
    }
    async initializeRedis() {
        try {
            logger_1.logger.info('Using in-memory cache (Redis disabled)');
            this.isConnected = false;
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize cache:', error);
            this.isConnected = false;
        }
    }
    generateFileHash(filePath, options = {}) {
        const fs = require('fs');
        try {
            const fileBuffer = fs.readFileSync(filePath);
            const optionsString = JSON.stringify(options);
            return crypto
                .createHash('sha256')
                .update(fileBuffer)
                .update(optionsString)
                .digest('hex');
        }
        catch (error) {
            logger_1.logger.error('Failed to generate file hash:', error);
            return crypto
                .createHash('sha256')
                .update(filePath + Date.now().toString())
                .digest('hex');
        }
    }
    getCacheKey(type, identifier, namespace) {
        const ns = namespace || this.namespace;
        return `${ns}:${type}:${identifier}`;
    }
    async cacheProcessingResult(filePath, options, result, cacheOptions = {}) {
        if (!this.isConnected || !this.client) {
            logger_1.logger.warn('Redis not connected, skipping cache');
            return false;
        }
        try {
            const fileHash = this.generateFileHash(filePath, options);
            const cacheKey = this.getCacheKey('processing', fileHash, cacheOptions.namespace);
            const cacheData = {
                fileHash,
                complexity: result.complexity,
                extractedData: result.extractedData,
                processingTime: result.processingTime,
                timestamp: Date.now(),
                version: '1.0'
            };
            const ttl = cacheOptions.ttl || this.defaultTTL;
            await this.client.setEx(cacheKey, ttl, JSON.stringify(cacheData));
            logger_1.logger.info(`Cached processing result for file hash: ${fileHash}`);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to cache processing result:', error);
            return false;
        }
    }
    async getCachedProcessingResult(filePath, options, namespace) {
        if (!this.isConnected || !this.client) {
            return null;
        }
        try {
            const fileHash = this.generateFileHash(filePath, options);
            const cacheKey = this.getCacheKey('processing', fileHash, namespace);
            const cachedData = await this.client.get(cacheKey);
            if (!cachedData) {
                return null;
            }
            const parsed = JSON.parse(cachedData);
            const age = Date.now() - parsed.timestamp;
            const maxAge = 24 * 60 * 60 * 1000;
            if (age > maxAge) {
                await this.client.del(cacheKey);
                return null;
            }
            logger_1.logger.info(`Cache hit for file hash: ${fileHash}`);
            return parsed;
        }
        catch (error) {
            logger_1.logger.error('Failed to get cached result:', error);
            return null;
        }
    }
    async cacheComplexityAnalysis(filePath, complexity, cacheOptions = {}) {
        if (!this.isConnected || !this.client) {
            return false;
        }
        try {
            const fileHash = this.generateFileHash(filePath);
            const cacheKey = this.getCacheKey('complexity', fileHash, cacheOptions.namespace);
            const cacheData = {
                fileHash,
                complexity,
                timestamp: Date.now()
            };
            const ttl = cacheOptions.ttl || this.defaultTTL * 2;
            await this.client.setEx(cacheKey, ttl, JSON.stringify(cacheData));
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to cache complexity analysis:', error);
            return false;
        }
    }
    async getCachedComplexityAnalysis(filePath, namespace) {
        if (!this.isConnected || !this.client) {
            return null;
        }
        try {
            const fileHash = this.generateFileHash(filePath);
            const cacheKey = this.getCacheKey('complexity', fileHash, namespace);
            const cachedData = await this.client.get(cacheKey);
            if (!cachedData) {
                return null;
            }
            const parsed = JSON.parse(cachedData);
            return parsed.complexity;
        }
        catch (error) {
            logger_1.logger.error('Failed to get cached complexity:', error);
            return null;
        }
    }
    async cacheUserSession(userId, sessionData, ttl = 3600) {
        if (!this.isConnected || !this.client) {
            return false;
        }
        try {
            const cacheKey = this.getCacheKey('session', userId);
            await this.client.setEx(cacheKey, ttl, JSON.stringify(sessionData));
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to cache user session:', error);
            return false;
        }
    }
    async getCachedUserSession(userId) {
        if (!this.isConnected || !this.client) {
            return null;
        }
        try {
            const cacheKey = this.getCacheKey('session', userId);
            const cachedData = await this.client.get(cacheKey);
            return cachedData ? JSON.parse(cachedData) : null;
        }
        catch (error) {
            logger_1.logger.error('Failed to get cached user session:', error);
            return null;
        }
    }
    async invalidateCache(pattern, namespace) {
        if (!this.isConnected || !this.client) {
            return 0;
        }
        try {
            const ns = namespace || this.namespace;
            const searchPattern = `${ns}:${pattern}`;
            const keys = await this.client.keys(searchPattern);
            if (keys.length === 0) {
                return 0;
            }
            const deleted = await this.client.del(keys);
            logger_1.logger.info(`Invalidated ${deleted} cache entries matching pattern: ${searchPattern}`);
            return deleted;
        }
        catch (error) {
            logger_1.logger.error('Failed to invalidate cache:', error);
            return 0;
        }
    }
    async getCacheStats() {
        if (!this.isConnected || !this.client) {
            return null;
        }
        try {
            const info = await this.client.info('memory');
            const keyspace = await this.client.info('keyspace');
            return {
                connected: this.isConnected,
                memory: info,
                keyspace: keyspace,
                namespace: this.namespace
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get cache stats:', error);
            return null;
        }
    }
    async clearAllCache() {
        if (!this.isConnected || !this.client) {
            return false;
        }
        try {
            await this.client.flushAll();
            logger_1.logger.info('All cache cleared');
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to clear cache:', error);
            return false;
        }
    }
    async disconnect() {
        if (this.client) {
            try {
                await this.client.quit();
                this.isConnected = false;
                logger_1.logger.info('Redis client disconnected');
            }
            catch (error) {
                logger_1.logger.error('Failed to disconnect Redis client:', error);
            }
        }
    }
}
exports.CacheService = CacheService;
//# sourceMappingURL=CacheService.js.map