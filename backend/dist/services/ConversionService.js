"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversionService = void 0;
const uuid_1 = require("uuid");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
const UserService_1 = require("./UserService");
const logger_1 = require("../utils/logger");
class ConversionService {
    constructor() {
        this.userService = new UserService_1.UserService();
    }
    async startConversion(data) {
        const { userId, file, options } = data;
        const conversionId = (0, uuid_1.v4)();
        const [conversion] = await (0, database_1.db)('conversions')
            .insert({
            id: conversionId,
            user_id: userId,
            file_name: file.originalname,
            file_size: file.size,
            file_path: file.path,
            status: 'pending',
            metadata: JSON.stringify(options)
        })
            .returning(['id', 'file_name', 'file_size', 'status', 'created_at']);
        logger_1.logger.info(`Conversion started: ${conversionId} for user: ${userId}`);
        this.processConversionAsync(conversionId, file, options);
        return {
            id: conversion.id,
            fileName: conversion.file_name,
            fileSize: conversion.file_size,
            status: conversion.status,
            createdAt: conversion.created_at
        };
    }
    async getConversions(userId, filters) {
        const { page, limit, status } = filters;
        const offset = (page - 1) * limit;
        let query = (0, database_1.db)('conversions')
            .where({ user_id: userId })
            .orderBy('created_at', 'desc');
        if (status) {
            query = query.where({ status });
        }
        const totalQuery = query.clone();
        const [{ count }] = await totalQuery.count('* as count');
        const total = parseInt(count);
        const conversions = await query
            .limit(limit)
            .offset(offset)
            .select(['id', 'file_name', 'file_size', 'status', 'created_at', 'processing_time', 'result_path']);
        return {
            conversions: conversions.map(c => ({
                id: c.id,
                fileName: c.file_name,
                fileSize: c.file_size,
                status: c.status,
                createdAt: c.created_at,
                processingTime: c.processing_time,
                resultPath: c.result_path
            })),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }
    async getConversion(conversionId, userId) {
        const conversion = await (0, database_1.db)('conversions')
            .where({ id: conversionId, user_id: userId })
            .first();
        if (!conversion) {
            throw new errorHandler_1.CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
        }
        return {
            id: conversion.id,
            fileName: conversion.file_name,
            fileSize: conversion.file_size,
            status: conversion.status,
            createdAt: conversion.created_at,
            processingTime: conversion.processing_time,
            resultPath: conversion.result_path
        };
    }
    async getDownloadPath(conversionId, userId) {
        const conversion = await (0, database_1.db)('conversions')
            .where({ id: conversionId, user_id: userId })
            .first();
        if (!conversion) {
            throw new errorHandler_1.CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
        }
        if (conversion.status !== 'completed' || !conversion.result_path) {
            throw new errorHandler_1.CustomError('Conversion not completed or file not available', 400, true, 'FILE_NOT_READY');
        }
        try {
            await promises_1.default.access(conversion.result_path);
            return conversion.result_path;
        }
        catch (error) {
            throw new errorHandler_1.CustomError('File not found', 404, true, 'FILE_NOT_FOUND');
        }
    }
    async deleteConversion(conversionId, userId) {
        const conversion = await (0, database_1.db)('conversions')
            .where({ id: conversionId, user_id: userId })
            .first();
        if (!conversion) {
            throw new errorHandler_1.CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
        }
        try {
            await promises_1.default.unlink(conversion.file_path);
            if (conversion.result_path) {
                await promises_1.default.unlink(conversion.result_path);
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to delete files for conversion ${conversionId}:`, error);
        }
        await (0, database_1.db)('conversions').where({ id: conversionId }).del();
        logger_1.logger.info(`Conversion deleted: ${conversionId}`);
    }
    async retryConversion(conversionId, userId) {
        const conversion = await (0, database_1.db)('conversions')
            .where({ id: conversionId, user_id: userId })
            .first();
        if (!conversion) {
            throw new errorHandler_1.CustomError('Conversion not found', 404, true, 'CONVERSION_NOT_FOUND');
        }
        if (conversion.status !== 'failed') {
            throw new errorHandler_1.CustomError('Only failed conversions can be retried', 400, true, 'INVALID_STATUS');
        }
        await (0, database_1.db)('conversions')
            .where({ id: conversionId })
            .update({
            status: 'pending',
            error_message: null
        });
        const file = {
            originalname: conversion.file_name,
            path: conversion.file_path,
            size: conversion.file_size
        };
        const options = conversion.metadata ? JSON.parse(conversion.metadata) : {};
        this.processConversionAsync(conversionId, file, options);
        logger_1.logger.info(`Conversion retry started: ${conversionId}`);
        return {
            id: conversion.id,
            fileName: conversion.file_name,
            fileSize: conversion.file_size,
            status: 'pending',
            createdAt: conversion.created_at
        };
    }
    async processConversionAsync(conversionId, file, options) {
        try {
            await (0, database_1.db)('conversions')
                .where({ id: conversionId })
                .update({ status: 'processing' });
            const processingTime = Math.random() * 5000 + 2000;
            await new Promise(resolve => setTimeout(resolve, processingTime));
            const resultPath = path_1.default.join(path_1.default.dirname(file.path), `result-${conversionId}.xlsx`);
            const mockExcelContent = 'Mock Excel content - will be replaced with real conversion';
            await promises_1.default.writeFile(resultPath, mockExcelContent);
            await (0, database_1.db)('conversions')
                .where({ id: conversionId })
                .update({
                status: 'completed',
                processing_time: Math.round(processingTime),
                result_path: resultPath
            });
            const conversion = await (0, database_1.db)('conversions').where({ id: conversionId }).first();
            if (conversion) {
                await this.userService.trackUsage(conversion.user_id, file.size, Math.round(processingTime));
            }
            logger_1.logger.info(`Conversion completed: ${conversionId}`);
        }
        catch (error) {
            logger_1.logger.error(`Conversion failed: ${conversionId}`, error);
            await (0, database_1.db)('conversions')
                .where({ id: conversionId })
                .update({
                status: 'failed',
                error_message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
}
exports.ConversionService = ConversionService;
//# sourceMappingURL=ConversionService.js.map