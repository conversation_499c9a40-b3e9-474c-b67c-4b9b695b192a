{"version": 3, "file": "OCRService.js", "sourceRoot": "", "sources": ["../../src/services/OCRService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAoD;AACpD,kDAA0B;AAC1B,qCAAsC;AACtC,gDAAkC;AAElC,4CAAyC;AA4CzC,MAAa,UAAU;IAIrB;QAHQ,WAAM,GAAkB,IAAI,CAAC;QAC7B,kBAAa,GAAG,KAAK,CAAC;IAI9B,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,UAAsB,EAAE;QACrD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,GAAG,MAAM,IAAA,2BAAY,EAAC,KAAK,CAAC,CAAC;YAExC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;YAG3C,MAAO,IAAI,CAAC,MAAc,CAAC,aAAa,CAAC;gBACvC,qBAAqB,EAAE,GAAG;gBAC1B,wBAAwB,EAAE,GAAG;aAC9B,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAErD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC5G,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,OAAe,EACf,UAAsB,EAAE;QAGxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,oCAAoC,OAAO,EAAE,CAAC,CAAC;YAG3D,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAGrC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAGnE,MAAM,OAAO,GAAoB,EAAE,CAAC;YAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChC,eAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,cAAc,CAAC,CAAC;gBAEzE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAErE,OAAO,CAAC,IAAI,CAAC;oBACX,UAAU,EAAE,CAAC,GAAG,CAAC;oBACjB,MAAM,EAAE,SAAS;oBACjB,SAAS,EAAE,SAAS;iBACrB,CAAC,CAAC;YACL,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,+BAA+B,SAAS,UAAU,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC;YAEtF,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,WAAmB,EACnB,UAAsB,EAAE;QAGxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;YAGD,IAAI,cAAc,GAAG,WAAW,CAAC;YACjC,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;gBACtC,cAAc,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,MAAO,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAE9D,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC;gBACH,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC;gBACH,cAAc;aACf,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAC9B,OAAe,EACf,UAAsB,EAAE;QAGxB,IAAI,CAAC;YAGH,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAGjD,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,qBAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;YAIxC,MAAM,MAAM,GAAa,EAAE,CAAC;YAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBAEnC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAClE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChC,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,aAAa,SAAS,sBAAsB,CAAC,CAAC;YAC1D,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAC3B,WAAmB,EACnB,UAAsB,EAAE;QAGxB,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC;YAG/B,IAAI,cAAc,GAAG,IAAA,eAAK,EAAC,WAAW,CAAC;iBACpC,SAAS,EAAE;iBACX,SAAS,EAAE;iBACX,OAAO,EAAE,CAAC;YAGb,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC5B,cAAc,GAAG,cAAc;qBAC5B,SAAS,CAAC,GAAG,CAAC;qBACd,MAAM,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;YAED,OAAO,MAAM,cAAc,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAE/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAEnD,OAAO,WAAW,CAAC;QACrB,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QACrD,IAAI,CAAC;YAEH,MAAM,GAAG,GAAG;;;;+BAIa,UAAU;;;;;OAKlC,CAAC;YAEF,OAAO,MAAM,IAAA,eAAK,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACjC,GAAG,EAAE;iBACL,QAAQ,EAAE,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC5C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,qBAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEjD,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;YAI3C,MAAM,eAAe,GAAG,WAAW,GAAG,MAAM,CAAC;YAE7C,eAAM,CAAC,IAAI,CAAC,sBAAsB,WAAW,oCAAoC,eAAe,EAAE,CAAC,CAAC;YAEpG,OAAO,eAAe,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,SAAS;YACT,KAAK;YACL,KAAK;SACN,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC3B,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAhSD,gCAgSC"}