{"version": 3, "file": "ComplexityAnalyzer.js", "sourceRoot": "", "sources": ["../../src/services/ComplexityAnalyzer.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AAuBzC,MAAa,kBAAkB;IAK7B,KAAK,CAAC,OAAO,CAAC,MAAmB,EAAE,SAAiB;QAClD,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAE/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,uBAAuB,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAE5E,eAAM,CAAC,IAAI,CAAC,kCAAkC,KAAK,YAAY,KAAK,GAAG,CAAC,CAAC;YAEzE,OAAO;gBACL,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,eAAe;gBACf,uBAAuB;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAGnD,OAAO;gBACL,KAAK,EAAE,QAAQ;gBACf,KAAK,EAAE,CAAC;gBACR,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBACjC,eAAe,EAAE,CAAC,kDAAkD,CAAC;gBACrE,uBAAuB,EAAE,IAAI;aAC9B,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,cAAc,CAAC,MAAmB,EAAE,SAAiB;QACjE,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;QAGlC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAGzB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YAEzB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC;YAIhC,WAAW,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAGxD,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAG5C,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAG5C,gBAAgB,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;QAGD,WAAW,GAAG,WAAW,GAAG,SAAS,CAAC;QACtC,gBAAgB,GAAG,gBAAgB,GAAG,SAAS,CAAC;QAGhD,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAGzE,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;QAClD,MAAM,cAAc,GAAG,KAAK,CAAC;QAG7B,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAE7C,OAAO;YACL,SAAS;YACT,WAAW;YACX,UAAU;YACV,UAAU;YACV,cAAc;YACd,gBAAgB;YAChB,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,QAAQ;SACT,CAAC;IACJ,CAAC;IAKO,wBAAwB,CAAC,OAA0B;QACzD,IAAI,KAAK,GAAG,CAAC,CAAC;QAGd,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAG7C,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAGhD,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAG9C,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAG9C,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,gBAAgB,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAGrD,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QAG9C,IAAI,OAAO,CAAC,iBAAiB;YAAE,KAAK,IAAI,CAAC,CAAC;QAC1C,IAAI,OAAO,CAAC,aAAa;YAAE,KAAK,IAAI,CAAC,CAAC;QACtC,IAAI,OAAO,CAAC,cAAc;YAAE,KAAK,IAAI,CAAC,CAAC;QAEvC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9B,CAAC;IAKO,wBAAwB,CAAC,KAAa;QAC5C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACjC,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,QAAQ,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,uBAAuB,CAAC,OAA0B,EAAE,KAAa;QACvE,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvB,eAAe,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACtE,CAAC;aAAM,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;YACtE,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBAC3B,eAAe,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAC9E,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC9B,eAAe,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAChF,CAAC;YACD,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBAC3B,eAAe,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,sBAAsB,CAAC,OAA0B,EAAE,KAAa;QACtE,IAAI,QAAQ,GAAG,IAAI,CAAC;QAGpB,QAAQ,IAAI,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC;QAGpC,IAAI,KAAK,KAAK,QAAQ;YAAE,QAAQ,IAAI,GAAG,CAAC;QACxC,IAAI,KAAK,KAAK,SAAS;YAAE,QAAQ,IAAI,GAAG,CAAC;QAGzC,IAAI,OAAO,CAAC,iBAAiB;YAAE,QAAQ,IAAI,IAAI,CAAC;QAChD,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC;YAAE,QAAQ,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;QAClE,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC;YAAE,QAAQ,IAAI,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC;QAEjE,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;IAIO,mBAAmB,CAAC,IAAS,EAAE,QAAgB;QAErD,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;IACnC,CAAC;IAEO,kBAAkB,CAAC,IAAS;QAElC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEO,kBAAkB,CAAC,IAAS;QAElC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IACvC,CAAC;IAEO,wBAAwB,CAAC,IAAS,EAAE,KAAa,EAAE,MAAc;QAEvE,MAAM,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC;QACnC,MAAM,cAAc,GAAG,CAAC,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,GAAG,CAAC,CAAC;QAChE,OAAO,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACpC,CAAC;IAEO,oBAAoB,CAAC,QAAgB,EAAE,SAAiB;QAE9D,MAAM,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;QACzC,OAAO,WAAW,GAAG,MAAM,CAAC;IAC9B,CAAC;IAEO,iBAAiB;QACvB,OAAO;YACL,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,CAAC;YACb,UAAU,EAAE,CAAC;YACb,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,GAAG;YACrB,iBAAiB,EAAE,KAAK;YACxB,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,KAAK;YACrB,QAAQ,EAAE,CAAC;SACZ,CAAC;IACJ,CAAC;CACF;AA3OD,gDA2OC"}