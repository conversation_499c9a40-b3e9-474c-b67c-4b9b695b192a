"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OCRService = void 0;
const tesseract_js_1 = require("tesseract.js");
const sharp_1 = __importDefault(require("sharp"));
const pdf_lib_1 = require("pdf-lib");
const fs = __importStar(require("fs/promises"));
const logger_1 = require("../utils/logger");
class OCRService {
    constructor() {
        this.worker = null;
        this.isInitialized = false;
    }
    async initializeWorker(options = {}) {
        if (this.isInitialized && this.worker) {
            return;
        }
        try {
            logger_1.logger.info('Initializing OCR worker...');
            this.worker = await (0, tesseract_js_1.createWorker)('eng');
            const language = options.language || 'eng';
            await this.worker.setParameters({
                tessedit_pageseg_mode: '1',
                tessedit_ocr_engine_mode: '3',
            });
            this.isInitialized = true;
            logger_1.logger.info('OCR worker initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize OCR worker:', error);
            throw new Error(`OCR initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async processPDFWithOCR(pdfPath, options = {}) {
        const startTime = Date.now();
        try {
            logger_1.logger.info(`Starting OCR processing for PDF: ${pdfPath}`);
            await this.initializeWorker(options);
            const pageImages = await this.convertPDFToImages(pdfPath, options);
            const results = [];
            for (let i = 0; i < pageImages.length; i++) {
                const pageImage = pageImages[i];
                logger_1.logger.info(`Processing page ${i + 1}/${pageImages.length} with OCR...`);
                const ocrResult = await this.processImageWithOCR(pageImage, options);
                results.push({
                    pageNumber: i + 1,
                    result: ocrResult,
                    imageData: pageImage
                });
            }
            const totalTime = Date.now() - startTime;
            logger_1.logger.info(`OCR processing completed in ${totalTime}ms for ${results.length} pages`);
            return results;
        }
        catch (error) {
            logger_1.logger.error('OCR processing failed:', error);
            throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async processImageWithOCR(imageBuffer, options = {}) {
        const startTime = Date.now();
        try {
            if (!this.worker) {
                await this.initializeWorker(options);
            }
            let processedImage = imageBuffer;
            if (options.preprocessImage !== false) {
                processedImage = await this.preprocessImage(imageBuffer, options);
            }
            const { data } = await this.worker.recognize(processedImage);
            const processingTime = Date.now() - startTime;
            return {
                text: data.text,
                confidence: data.confidence,
                words: data.words.map(word => ({
                    text: word.text,
                    confidence: word.confidence,
                    bbox: word.bbox
                })),
                lines: data.lines.map(line => ({
                    text: line.text,
                    confidence: line.confidence,
                    bbox: line.bbox
                })),
                processingTime
            };
        }
        catch (error) {
            logger_1.logger.error('Image OCR processing failed:', error);
            throw new Error(`Image OCR failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async convertPDFToImages(pdfPath, options = {}) {
        try {
            logger_1.logger.info('Converting PDF pages to images...');
            const pdfBuffer = await fs.readFile(pdfPath);
            const pdfDoc = await pdf_lib_1.PDFDocument.load(pdfBuffer);
            const pageCount = pdfDoc.getPageCount();
            const images = [];
            for (let i = 0; i < pageCount; i++) {
                const placeholderImage = await this.createPlaceholderImage(i + 1);
                images.push(placeholderImage);
            }
            logger_1.logger.info(`Converted ${pageCount} PDF pages to images`);
            return images;
        }
        catch (error) {
            logger_1.logger.error('PDF to image conversion failed:', error);
            throw new Error(`PDF conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async preprocessImage(imageBuffer, options = {}) {
        try {
            const dpi = options.dpi || 300;
            let processedImage = (0, sharp_1.default)(imageBuffer)
                .greyscale()
                .normalize()
                .sharpen();
            if (options.preprocessImage) {
                processedImage = processedImage
                    .threshold(128)
                    .median(3);
            }
            return await processedImage.png().toBuffer();
        }
        catch (error) {
            logger_1.logger.error('Image preprocessing failed:', error);
            return imageBuffer;
        }
    }
    async createPlaceholderImage(pageNumber) {
        try {
            const svg = `
        <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="white"/>
          <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="24" fill="black">
            Placeholder Page ${pageNumber}
            This is sample text for OCR testing.
            In real implementation, this would be the actual PDF page image.
          </text>
        </svg>
      `;
            return await (0, sharp_1.default)(Buffer.from(svg))
                .png()
                .toBuffer();
        }
        catch (error) {
            logger_1.logger.error('Failed to create placeholder image:', error);
            throw error;
        }
    }
    static async isScannedDocument(pdfPath) {
        try {
            const stats = await fs.stat(pdfPath);
            const pdfBuffer = await fs.readFile(pdfPath);
            const pdfDoc = await pdf_lib_1.PDFDocument.load(pdfBuffer);
            const pageCount = pdfDoc.getPageCount();
            const sizePerPage = stats.size / pageCount;
            const isLikelyScanned = sizePerPage > 500000;
            logger_1.logger.info(`Document analysis: ${sizePerPage} bytes per page, likely scanned: ${isLikelyScanned}`);
            return isLikelyScanned;
        }
        catch (error) {
            logger_1.logger.error('Failed to analyze document:', error);
            return false;
        }
    }
    static getSupportedLanguages() {
        return [
            'eng',
            'pol',
            'deu',
            'fra',
            'spa',
            'ita',
            'rus',
            'chi_sim',
            'jpn',
            'kor'
        ];
    }
    async cleanup() {
        if (this.worker) {
            try {
                await this.worker.terminate();
                this.worker = null;
                this.isInitialized = false;
                logger_1.logger.info('OCR worker terminated');
            }
            catch (error) {
                logger_1.logger.error('Failed to terminate OCR worker:', error);
            }
        }
    }
}
exports.OCRService = OCRService;
//# sourceMappingURL=OCRService.js.map