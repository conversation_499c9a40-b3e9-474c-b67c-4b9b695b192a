export interface OCROptions {
    language?: string;
    psm?: number;
    oem?: number;
    whitelist?: string;
    blacklist?: string;
    dpi?: number;
    preprocessImage?: boolean;
}
export interface OCRResult {
    text: string;
    confidence: number;
    words: Array<{
        text: string;
        confidence: number;
        bbox: {
            x0: number;
            y0: number;
            x1: number;
            y1: number;
        };
    }>;
    lines: Array<{
        text: string;
        confidence: number;
        bbox: {
            x0: number;
            y0: number;
            x1: number;
            y1: number;
        };
    }>;
    processingTime: number;
}
export interface PageOCRResult {
    pageNumber: number;
    result: OCRResult;
    imageData?: Buffer;
}
export declare class OCRService {
    private worker;
    private isInitialized;
    constructor();
    private initializeWorker;
    processPDFWithOCR(pdfPath: string, options?: OCROptions): Promise<PageOCRResult[]>;
    processImageWithOCR(imageBuffer: Buffer, options?: OCROptions): Promise<OCRResult>;
    private convertPDFToImages;
    private preprocessImage;
    private createPlaceholderImage;
    static isScannedDocument(pdfPath: string): Promise<boolean>;
    static getSupportedLanguages(): string[];
    cleanup(): Promise<void>;
}
//# sourceMappingURL=OCRService.d.ts.map