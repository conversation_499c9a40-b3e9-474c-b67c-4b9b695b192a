"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplexityAnalyzer = void 0;
const logger_1 = require("../utils/logger");
class ComplexityAnalyzer {
    async analyze(pdfDoc, pdfBuffer) {
        try {
            logger_1.logger.info('Starting complexity analysis...');
            const metrics = await this.extractMetrics(pdfDoc, pdfBuffer);
            const score = this.calculateComplexityScore(metrics);
            const level = this.determineComplexityLevel(score);
            const recommendations = this.generateRecommendations(metrics, level);
            const estimatedProcessingTime = this.estimateProcessingTime(metrics, level);
            logger_1.logger.info(`Complexity analysis completed: ${level} (score: ${score})`);
            return {
                level,
                score,
                metrics,
                recommendations,
                estimatedProcessingTime
            };
        }
        catch (error) {
            logger_1.logger.error('Complexity analysis failed:', error);
            return {
                level: 'simple',
                score: 0,
                metrics: this.getDefaultMetrics(),
                recommendations: ['Document analysis failed, using basic processing'],
                estimatedProcessingTime: 5000
            };
        }
    }
    async extractMetrics(pdfDoc, pdfBuffer) {
        const pages = pdfDoc.getPages();
        const pageCount = pages.length;
        const fileSize = pdfBuffer.length;
        let textDensity = 0;
        let imageCount = 0;
        let tableCount = 0;
        let fontVariations = 0;
        let layoutComplexity = 0;
        for (const page of pages) {
            const { width, height } = page.getSize();
            const pageArea = width * height;
            textDensity += this.estimateTextDensity(page, pageArea);
            imageCount += this.estimateImageCount(page);
            tableCount += this.estimateTableCount(page);
            layoutComplexity += this.estimateLayoutComplexity(page, width, height);
        }
        textDensity = textDensity / pageCount;
        layoutComplexity = layoutComplexity / pageCount;
        const hasScannedContent = this.detectScannedContent(fileSize, pageCount);
        const form = pdfDoc.getForm();
        const hasFormFields = form.getFields().length > 0;
        const hasAnnotations = false;
        fontVariations = Math.min(pageCount * 2, 10);
        return {
            pageCount,
            textDensity,
            imageCount,
            tableCount,
            fontVariations,
            layoutComplexity,
            hasScannedContent,
            hasFormFields,
            hasAnnotations,
            fileSize
        };
    }
    calculateComplexityScore(metrics) {
        let score = 0;
        score += Math.min(metrics.pageCount * 2, 20);
        score += Math.min(metrics.textDensity * 15, 15);
        score += Math.min(metrics.imageCount * 3, 15);
        score += Math.min(metrics.tableCount * 5, 20);
        score += Math.min(metrics.layoutComplexity * 15, 15);
        score += Math.min(metrics.fontVariations, 10);
        if (metrics.hasScannedContent)
            score += 5;
        if (metrics.hasFormFields)
            score += 3;
        if (metrics.hasAnnotations)
            score += 2;
        return Math.min(score, 100);
    }
    determineComplexityLevel(score) {
        if (score <= 30)
            return 'simple';
        if (score <= 60)
            return 'medium';
        return 'complex';
    }
    generateRecommendations(metrics, level) {
        const recommendations = [];
        if (level === 'simple') {
            recommendations.push('Document suitable for fast local processing');
        }
        else if (level === 'medium') {
            recommendations.push('Document may benefit from enhanced processing');
            if (metrics.tableCount > 3) {
                recommendations.push('Multiple tables detected - using table-specific extraction');
            }
        }
        else {
            recommendations.push('Complex document - may require specialized processing');
            if (metrics.hasScannedContent) {
                recommendations.push('Scanned content detected - OCR processing recommended');
            }
            if (metrics.imageCount > 5) {
                recommendations.push('Multiple images detected - image analysis may be needed');
            }
        }
        return recommendations;
    }
    estimateProcessingTime(metrics, level) {
        let baseTime = 2000;
        baseTime += metrics.pageCount * 500;
        if (level === 'medium')
            baseTime *= 1.5;
        if (level === 'complex')
            baseTime *= 2.5;
        if (metrics.hasScannedContent)
            baseTime += 3000;
        if (metrics.tableCount > 0)
            baseTime += metrics.tableCount * 1000;
        if (metrics.imageCount > 0)
            baseTime += metrics.imageCount * 500;
        return Math.min(baseTime, 30000);
    }
    estimateTextDensity(page, pageArea) {
        return Math.random() * 0.7 + 0.1;
    }
    estimateImageCount(page) {
        return Math.floor(Math.random() * 3);
    }
    estimateTableCount(page) {
        return Math.floor(Math.random() * 2);
    }
    estimateLayoutComplexity(page, width, height) {
        const aspectRatio = width / height;
        const isStandardSize = (aspectRatio > 0.7 && aspectRatio < 0.8);
        return isStandardSize ? 0.3 : 0.7;
    }
    detectScannedContent(fileSize, pageCount) {
        const sizePerPage = fileSize / pageCount;
        return sizePerPage > 500000;
    }
    getDefaultMetrics() {
        return {
            pageCount: 1,
            textDensity: 0.5,
            imageCount: 0,
            tableCount: 0,
            fontVariations: 1,
            layoutComplexity: 0.3,
            hasScannedContent: false,
            hasFormFields: false,
            hasAnnotations: false,
            fileSize: 0
        };
    }
}
exports.ComplexityAnalyzer = ComplexityAnalyzer;
//# sourceMappingURL=ComplexityAnalyzer.js.map