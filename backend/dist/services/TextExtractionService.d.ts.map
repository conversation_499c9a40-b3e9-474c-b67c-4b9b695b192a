{"version": 3, "file": "TextExtractionService.d.ts", "sourceRoot": "", "sources": ["../../src/services/TextExtractionService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAItC,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE;QACR,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;QACV,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,SAAS;IACxB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;IACjB,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM,CAAC;QACb,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,EAAE,MAAM,CAAC;KACX,CAAC;IACF,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,aAAa,EAAE,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,CAAC;IACpB,QAAQ,EAAE;QACR,UAAU,EAAE,MAAM,CAAC;QACnB,gBAAgB,EAAE,MAAM,CAAC;QACzB,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;CACH;AAED,qBAAa,qBAAqB;;IAe1B,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,gBAAgB,CAAC;YAqCtE,gBAAgB;IAyCxB,YAAY,CAAC,gBAAgB,EAAE,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;IAuB/D,OAAO,CAAC,gBAAgB;IAiBxB,OAAO,CAAC,kBAAkB;IA+B1B,OAAO,CAAC,iBAAiB;IAkCzB,OAAO,CAAC,mBAAmB;IA4C3B,OAAO,CAAC,wBAAwB;IA+BhC,OAAO,CAAC,qBAAqB;IAsB7B,OAAO,CAAC,uBAAuB;IAmB/B,OAAO,CAAC,gBAAgB;IAQxB,OAAO,CAAC,kBAAkB;CAmB3B"}