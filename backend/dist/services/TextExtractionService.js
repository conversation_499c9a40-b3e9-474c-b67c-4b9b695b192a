"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextExtractionService = void 0;
const pdfjsLib = __importStar(require("pdfjs-dist"));
const logger_1 = require("../utils/logger");
class TextExtractionService {
    constructor() {
        try {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdfjs-dist/build/pdf.worker.js';
        }
        catch (error) {
            logger_1.logger.warn('PDF.js worker setup failed, using fallback');
        }
    }
    async extractText(pdfDoc, pdfBuffer) {
        const startTime = Date.now();
        try {
            logger_1.logger.info('Starting text extraction...');
            const extractedText = await this.extractWithPDFJS(pdfBuffer);
            const tables = await this.detectTables(extractedText);
            const processingTime = Date.now() - startTime;
            logger_1.logger.info(`Text extraction completed in ${processingTime}ms`);
            return {
                text: extractedText.text,
                tables,
                metadata: {
                    totalPages: pdfDoc.getPageCount(),
                    extractionMethod: 'pdfjs',
                    processingTime
                }
            };
        }
        catch (error) {
            logger_1.logger.error('Text extraction failed:', error);
            return this.fallbackExtraction(pdfDoc, Date.now() - startTime);
        }
    }
    async extractWithPDFJS(pdfBuffer) {
        const extractedText = [];
        try {
            const loadingTask = pdfjsLib.getDocument({ data: pdfBuffer });
            const pdf = await loadingTask.promise;
            for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                const page = await pdf.getPage(pageNum);
                const textContent = await page.getTextContent();
                textContent.items.forEach((item, index) => {
                    if (item.str && item.str.trim()) {
                        extractedText.push({
                            content: item.str,
                            page: pageNum,
                            position: {
                                x: item.transform[4] || 0,
                                y: item.transform[5] || 0,
                                width: item.width || 0,
                                height: item.height || 0
                            },
                            fontSize: item.height || 12,
                            fontName: item.fontName || 'unknown'
                        });
                    }
                });
            }
        }
        catch (error) {
            logger_1.logger.error('PDF.js extraction failed:', error);
            throw error;
        }
        return { text: extractedText };
    }
    async detectTables(extractionResult) {
        const tables = [];
        try {
            const pageTexts = this.groupTextByPages(extractionResult.text || []);
            for (const [pageNum, pageText] of pageTexts.entries()) {
                const pageTables = this.detectTablesInPage(pageText, pageNum + 1);
                tables.push(...pageTables);
            }
        }
        catch (error) {
            logger_1.logger.error('Table detection failed:', error);
        }
        return tables;
    }
    groupTextByPages(textItems) {
        const pages = [];
        textItems.forEach(item => {
            const pageIndex = item.page - 1;
            if (!pages[pageIndex]) {
                pages[pageIndex] = [];
            }
            pages[pageIndex].push(item);
        });
        return pages;
    }
    detectTablesInPage(pageText, pageNum) {
        const tables = [];
        if (!pageText || pageText.length === 0) {
            return tables;
        }
        const sortedText = pageText.sort((a, b) => {
            if (Math.abs(a.position.y - b.position.y) < 5) {
                return a.position.x - b.position.x;
            }
            return b.position.y - a.position.y;
        });
        const rows = this.groupTextIntoRows(sortedText);
        const tableCandidate = this.analyzeRowsForTable(rows, pageNum);
        if (tableCandidate) {
            tables.push(tableCandidate);
        }
        return tables;
    }
    groupTextIntoRows(textItems) {
        const rows = [];
        const tolerance = 5;
        textItems.forEach(item => {
            let addedToRow = false;
            for (const row of rows) {
                if (row.length > 0) {
                    const rowY = row[0].position.y;
                    if (Math.abs(item.position.y - rowY) <= tolerance) {
                        row.push(item);
                        addedToRow = true;
                        break;
                    }
                }
            }
            if (!addedToRow) {
                rows.push([item]);
            }
        });
        rows.forEach(row => {
            row.sort((a, b) => a.position.x - b.position.x);
        });
        return rows;
    }
    analyzeRowsForTable(rows, pageNum) {
        if (rows.length < 2) {
            return null;
        }
        const columnCounts = rows.map(row => row.length);
        const avgColumns = columnCounts.reduce((a, b) => a + b, 0) / columnCounts.length;
        const tableRows = rows.filter(row => Math.abs(row.length - avgColumns) <= 1);
        if (tableRows.length < 2) {
            return null;
        }
        const headers = tableRows[0].map(item => item.content.trim());
        const dataRows = tableRows.slice(1).map(row => row.map(item => item.content.trim()));
        const confidence = this.calculateTableConfidence(tableRows);
        if (confidence < 0.5) {
            return null;
        }
        return {
            headers,
            rows: dataRows,
            position: {
                page: pageNum,
                x: Math.min(...tableRows[0].map(item => item.position.x)),
                y: Math.max(...tableRows[0].map(item => item.position.y))
            },
            confidence
        };
    }
    calculateTableConfidence(rows) {
        if (rows.length < 2)
            return 0;
        let score = 0;
        const maxScore = 100;
        const columnCounts = rows.map(row => row.length);
        const avgColumns = columnCounts.reduce((a, b) => a + b, 0) / columnCounts.length;
        const columnConsistency = columnCounts.filter(count => Math.abs(count - avgColumns) <= 1).length / columnCounts.length;
        score += columnConsistency * 40;
        const alignmentScore = this.calculateRowAlignment(rows);
        score += alignmentScore * 30;
        const numericScore = this.calculateNumericContent(rows);
        score += numericScore * 20;
        if (rows.length >= 3 && avgColumns >= 2) {
            score += 10;
        }
        return Math.min(score, maxScore) / maxScore;
    }
    calculateRowAlignment(rows) {
        if (rows.length < 2)
            return 0;
        let alignmentScore = 0;
        const tolerance = 10;
        for (let col = 0; col < Math.min(...rows.map(r => r.length)); col++) {
            const xPositions = rows.map(row => row[col]?.position.x).filter(x => x !== undefined);
            if (xPositions.length < 2)
                continue;
            const avgX = xPositions.reduce((a, b) => a + b, 0) / xPositions.length;
            const aligned = xPositions.filter(x => Math.abs(x - avgX) <= tolerance).length;
            alignmentScore += aligned / xPositions.length;
        }
        return alignmentScore / Math.min(...rows.map(r => r.length));
    }
    calculateNumericContent(rows) {
        let totalCells = 0;
        let numericCells = 0;
        rows.forEach(row => {
            row.forEach(cell => {
                totalCells++;
                if (this.isNumericContent(cell.content)) {
                    numericCells++;
                }
            });
        });
        return totalCells > 0 ? numericCells / totalCells : 0;
    }
    isNumericContent(content) {
        const cleaned = content.replace(/[,.\s$%€]/g, '');
        return /^\d+$/.test(cleaned) && cleaned.length > 0;
    }
    fallbackExtraction(pdfDoc, processingTime) {
        logger_1.logger.info('Using fallback text extraction');
        return {
            text: [{
                    content: 'Text extraction failed - using fallback method',
                    page: 1,
                    position: { x: 0, y: 0, width: 0, height: 0 },
                    fontSize: 12,
                    fontName: 'unknown'
                }],
            tables: [],
            metadata: {
                totalPages: pdfDoc.getPageCount(),
                extractionMethod: 'fallback',
                processingTime
            }
        };
    }
}
exports.TextExtractionService = TextExtractionService;
//# sourceMappingURL=TextExtractionService.js.map