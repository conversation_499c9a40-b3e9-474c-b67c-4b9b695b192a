{"version": 3, "file": "TextExtractionService.js", "sourceRoot": "", "sources": ["../../src/services/TextExtractionService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,qDAAuC;AACvC,4CAAyC;AAoCzC,MAAa,qBAAqB;IAEhC;QAGE,IAAI,CAAC;YACF,QAAgB,CAAC,mBAAmB,CAAC,SAAS,GAAG,gCAAgC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,MAAmB,EAAE,SAAiB;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAG3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAG7D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YAEtD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,eAAM,CAAC,IAAI,CAAC,gCAAgC,cAAc,IAAI,CAAC,CAAC;YAEhE,OAAO;gBACL,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,MAAM;gBACN,QAAQ,EAAE;oBACR,UAAU,EAAE,MAAM,CAAC,YAAY,EAAE;oBACjC,gBAAgB,EAAE,OAAO;oBACzB,cAAc;iBACf;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAG/C,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC9C,MAAM,aAAa,GAAoB,EAAE,CAAC;QAE1C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;YAC9D,MAAM,GAAG,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC;YAEtC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,CAAC;gBACzD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAGhD,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE;oBACrD,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;wBAChC,aAAa,CAAC,IAAI,CAAC;4BACjB,OAAO,EAAE,IAAI,CAAC,GAAG;4BACjB,IAAI,EAAE,OAAO;4BACb,QAAQ,EAAE;gCACR,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;gCACzB,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;gCACzB,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC;gCACtB,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;6BACzB;4BACD,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE;4BAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,SAAS;yBACrC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACjC,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,gBAAqB;QACtC,MAAM,MAAM,GAAgB,EAAE,CAAC;QAE/B,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAGrE,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC;gBACtD,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;gBAClE,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;YAC7B,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,gBAAgB,CAAC,SAA0B;QACjD,MAAM,KAAK,GAAsB,EAAE,CAAC;QAEpC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtB,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YACxB,CAAC;YACD,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,kBAAkB,CAAC,QAAyB,EAAE,OAAe;QACnE,MAAM,MAAM,GAAgB,EAAE,CAAC;QAE/B,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,MAAM,CAAC;QAChB,CAAC;QAGD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrC,CAAC;YACD,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAGH,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAGhD,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAE/D,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,iBAAiB,CAAC,SAA0B;QAClD,MAAM,IAAI,GAAsB,EAAE,CAAC;QACnC,MAAM,SAAS,GAAG,CAAC,CAAC;QAEpB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,UAAU,GAAG,KAAK,CAAC;YAEvB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACnB,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;oBAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,SAAS,EAAE,CAAC;wBAClD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACf,UAAU,GAAG,IAAI,CAAC;wBAClB,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,mBAAmB,CAAC,IAAuB,EAAE,OAAe;QAClE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;QAGjF,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAE7E,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC5C,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CACrC,CAAC;QAGF,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,OAAO;YACP,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE;gBACR,IAAI,EAAE,OAAO;gBACb,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACzD,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC1D;YACD,UAAU;SACX,CAAC;IACJ,CAAC;IAKO,wBAAwB,CAAC,IAAuB;QACtD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,QAAQ,GAAG,GAAG,CAAC;QAGrB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;QACjF,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACvH,KAAK,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAGhC,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACxD,KAAK,IAAI,cAAc,GAAG,EAAE,CAAC;QAG7B,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACxD,KAAK,IAAI,YAAY,GAAG,EAAE,CAAC;QAG3B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YACxC,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAC;IAC9C,CAAC;IAKO,qBAAqB,CAAC,IAAuB;QACnD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,SAAS,GAAG,EAAE,CAAC;QAGrB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;YACpE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;YACtF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC;gBAAE,SAAS;YAEpC,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;YACvE,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC;YAC/E,cAAc,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;QAChD,CAAC;QAED,OAAO,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/D,CAAC;IAKO,uBAAuB,CAAC,IAAuB;QACrD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACjB,UAAU,EAAE,CAAC;gBACb,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxC,YAAY,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC;IAKO,gBAAgB,CAAC,OAAe;QACtC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IACrD,CAAC;IAKO,kBAAkB,CAAC,MAAmB,EAAE,cAAsB;QACpE,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAE9C,OAAO;YACL,IAAI,EAAE,CAAC;oBACL,OAAO,EAAE,gDAAgD;oBACzD,IAAI,EAAE,CAAC;oBACP,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBAC7C,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,SAAS;iBACpB,CAAC;YACF,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE;gBACR,UAAU,EAAE,MAAM,CAAC,YAAY,EAAE;gBACjC,gBAAgB,EAAE,UAAU;gBAC5B,cAAc;aACf;SACF,CAAC;IACJ,CAAC;CACF;AArVD,sDAqVC"}