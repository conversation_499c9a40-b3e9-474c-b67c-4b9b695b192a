{"version": 3, "file": "PDFProcessingService.js", "sourceRoot": "", "sources": ["../../src/services/PDFProcessingService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAA2C;AAC3C,gDAAkC;AAElC,4CAAyC;AACzC,mEAAgE;AAChE,6DAA0D;AAC1D,mEAAgE;AA0BhE,MAAa,oBAAoB;IAK/B;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,6CAAqB,EAAE,CAAC;QAClD,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,IAAI,6CAAqB,EAAE,CAAC;IACnD,CAAC;IAKD,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,OAA0B,EAC1B,gBAAyD;QAEzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;YAGpD,gBAAgB,EAAE,CAAC;gBACjB,KAAK,EAAE,WAAW;gBAClB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,kCAAkC;aAC5C,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,MAAM,qBAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEjD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC5E,eAAM,CAAC,IAAI,CAAC,wBAAwB,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;YAGxD,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAEjE,gBAAgB,EAAE,CAAC;gBACjB,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,yBAAyB,gBAAgB,gBAAgB;aACnE,CAAC,CAAC;YAGH,IAAI,aAAa,CAAC;YAClB,IAAI,gBAAgB,KAAK,OAAO,EAAE,CAAC;gBACjC,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;YAC1F,CAAC;iBAAM,IAAI,gBAAgB,KAAK,OAAO,EAAE,CAAC;gBACxC,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACpF,CAAC;iBAAM,CAAC;gBACN,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACzF,CAAC;YAGD,gBAAgB,EAAE,CAAC;gBACjB,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,0BAA0B;aACpC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CACxD,aAAa,EACb,QAAQ,EACR,OAAO,CACR,CAAC;YAEF,gBAAgB,EAAE,CAAC;gBACjB,KAAK,EAAE,WAAW;gBAClB,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,oCAAoC;aAC9C,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,UAAU;gBACV,aAAa;gBACb,cAAc;gBACd,UAAU,EAAE,UAAU,CAAC,KAAK;gBAC5B,MAAM,EAAE,gBAAgB;aACzB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAE9C,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,gBAAgB,EAAE,CAAC;gBACjB,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,sBAAsB,YAAY,EAAE;aAC9C,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,UAAU,EAAE,QAAQ;gBACpB,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,sBAAsB,CAAC,UAAe;QAI5C,IAAI,UAAU,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,IAAI,UAAU,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,cAAc,CAC1B,MAAmB,EACnB,SAAiB,EACjB,OAA0B,EAC1B,gBAAyD;QAGzD,gBAAgB,EAAE,CAAC;YACjB,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,4BAA4B;SACtC,CAAC,CAAC;QAGH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEzE,gBAAgB,EAAE,CAAC;YACjB,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,mCAAmC;SAC7C,CAAC,CAAC;QAGH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAEvE,gBAAgB,EAAE,CAAC;YACjB,KAAK,EAAE,YAAY;YACnB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,8BAA8B;SACxC,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAEzE,OAAO,aAAa,CAAC;IACvB,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAC5B,SAAiB,EACjB,OAA0B,EAC1B,gBAAyD;QAIzD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;IAC1D,CAAC;IAKO,KAAK,CAAC,aAAa,CACzB,MAAmB,EACnB,SAAiB,EACjB,OAA0B,EAC1B,gBAAyD;QAIzD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAC3E,CAAC;IAKO,oBAAoB,CAAC,OAAY,EAAE,OAA0B;QAEnE,MAAM,aAAa,GAAG;YACpB,MAAM,EAAE,EAAE;YACV,IAAI,EAAE,EAAE;YACR,QAAQ,EAAE;gBACR,gBAAgB,EAAE,OAAO;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO;aACR;SACF,CAAC;QAGF,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBACzD,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;gBACtB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;aACpD,CAAC,CAAC,CAAC;QACN,CAAC;QAGD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,aAAa,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAKD,MAAM,CAAC,mBAAmB;QACxB,OAAO,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,qBAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAlPD,oDAkPC"}