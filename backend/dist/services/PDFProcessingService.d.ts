export interface ProcessingOptions {
    template?: string;
    outputFormat?: 'xlsx' | 'csv';
    extractTables?: boolean;
    extractText?: boolean;
    ocrEnabled?: boolean;
}
export interface ProcessingResult {
    success: boolean;
    outputPath?: string;
    extractedData?: any;
    processingTime: number;
    complexity: 'simple' | 'medium' | 'complex';
    method: 'local' | 'cloud' | 'hybrid';
    error?: string;
}
export interface ProcessingProgress {
    stage: 'analyzing' | 'extracting' | 'processing' | 'generating' | 'completed' | 'failed';
    progress: number;
    message: string;
}
export declare class PDFProcessingService {
    private excelGenerator;
    private complexityAnalyzer;
    private textExtractor;
    constructor();
    processPDF(filePath: string, options: ProcessingOptions, progressCallback?: (progress: ProcessingProgress) => void): Promise<ProcessingResult>;
    private selectProcessingMethod;
    private processLocally;
    private processWithCloud;
    private processHybrid;
    private processExtractedData;
    static getSupportedFormats(): string[];
    static validatePDF(filePath: string): Promise<boolean>;
}
//# sourceMappingURL=PDFProcessingService.d.ts.map