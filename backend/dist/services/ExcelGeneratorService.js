"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExcelGeneratorService = void 0;
const ExcelJS = __importStar(require("exceljs"));
const path = __importStar(require("path"));
const logger_1 = require("../utils/logger");
class ExcelGeneratorService {
    async generateExcel(extractedData, originalFilePath, options) {
        try {
            logger_1.logger.info('Starting Excel generation...');
            const workbook = new ExcelJS.Workbook();
            this.setWorkbookProperties(workbook, originalFilePath);
            await this.addDataWorksheet(workbook, extractedData, options);
            if (options.extractText) {
                await this.addMetadataWorksheet(workbook, extractedData);
            }
            const outputPath = this.generateOutputPath(originalFilePath);
            await workbook.xlsx.writeFile(outputPath);
            logger_1.logger.info(`Excel file generated: ${outputPath}`);
            return outputPath;
        }
        catch (error) {
            logger_1.logger.error('Excel generation failed:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new Error(`Failed to generate Excel file: ${errorMessage}`);
        }
    }
    setWorkbookProperties(workbook, originalFilePath) {
        const fileName = path.basename(originalFilePath, path.extname(originalFilePath));
        workbook.creator = 'PDF-Excel SaaS';
        workbook.lastModifiedBy = 'PDF-Excel SaaS';
        workbook.created = new Date();
        workbook.modified = new Date();
        workbook.lastPrinted = new Date();
        const props = workbook.properties;
        props.title = `Converted from ${fileName}`;
        props.subject = 'PDF to Excel Conversion';
        props.keywords = 'PDF, Excel, Conversion, Data Extraction';
        props.category = 'Data Processing';
        props.description = `Excel file generated from PDF: ${fileName}`;
    }
    async addDataWorksheet(workbook, extractedData, options) {
        const worksheet = workbook.addWorksheet('Extracted Data');
        worksheet.properties.defaultRowHeight = 20;
        worksheet.views = [{ showGridLines: true }];
        let currentRow = 1;
        if (extractedData.tables && extractedData.tables.length > 0) {
            currentRow = await this.addTablesToWorksheet(worksheet, extractedData.tables, currentRow);
        }
        if ((!extractedData.tables || extractedData.tables.length === 0) || options.extractText) {
            currentRow = await this.addTextToWorksheet(worksheet, extractedData.text, currentRow);
        }
        this.autoFitColumns(worksheet);
        this.applyWorksheetFormatting(worksheet);
    }
    async addTablesToWorksheet(worksheet, tables, startRow) {
        let currentRow = startRow;
        for (let i = 0; i < tables.length; i++) {
            const table = tables[i];
            if (tables.length > 1) {
                const titleCell = worksheet.getCell(currentRow, 1);
                titleCell.value = `Table ${i + 1} (Page ${table.position?.page || 'Unknown'})`;
                titleCell.font = { bold: true, size: 14 };
                titleCell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFE6E6FA' }
                };
                currentRow += 2;
            }
            if (table.headers && table.headers.length > 0) {
                table.headers.forEach((header, colIndex) => {
                    const cell = worksheet.getCell(currentRow, colIndex + 1);
                    cell.value = header;
                    cell.font = { bold: true };
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFF0F8FF' }
                    };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
                currentRow++;
            }
            if (table.rows && table.rows.length > 0) {
                table.rows.forEach((row) => {
                    row.forEach((cellValue, colIndex) => {
                        const cell = worksheet.getCell(currentRow, colIndex + 1);
                        const numericValue = this.parseNumericValue(cellValue);
                        cell.value = numericValue !== null ? numericValue : cellValue;
                        cell.border = {
                            top: { style: 'thin' },
                            left: { style: 'thin' },
                            bottom: { style: 'thin' },
                            right: { style: 'thin' }
                        };
                        if (numericValue !== null) {
                            cell.numFmt = '#,##0.00';
                        }
                    });
                    currentRow++;
                });
            }
            currentRow += 2;
        }
        return currentRow;
    }
    async addTextToWorksheet(worksheet, textItems, startRow) {
        let currentRow = startRow;
        if (!textItems || textItems.length === 0) {
            const cell = worksheet.getCell(currentRow, 1);
            cell.value = 'No text content extracted';
            cell.font = { italic: true };
            return currentRow + 1;
        }
        const titleCell = worksheet.getCell(currentRow, 1);
        titleCell.value = 'Extracted Text Content';
        titleCell.font = { bold: true, size: 14 };
        titleCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFEFD5' }
        };
        currentRow += 2;
        const pageGroups = this.groupTextByPages(textItems);
        for (const [pageNum, pageText] of pageGroups.entries()) {
            const pageHeaderCell = worksheet.getCell(currentRow, 1);
            pageHeaderCell.value = `Page ${pageNum + 1}`;
            pageHeaderCell.font = { bold: true };
            currentRow++;
            const combinedText = pageText.map(item => item.content).join(' ');
            const textCell = worksheet.getCell(currentRow, 1);
            textCell.value = combinedText;
            textCell.alignment = { wrapText: true, vertical: 'top' };
            worksheet.getRow(currentRow).height = Math.min(Math.max(combinedText.length / 50, 20), 100);
            currentRow += 2;
        }
        return currentRow;
    }
    async addMetadataWorksheet(workbook, extractedData) {
        const worksheet = workbook.addWorksheet('Metadata');
        const metadata = extractedData.metadata || {};
        const metadataItems = [
            ['Extraction Method', metadata.extractionMethod || 'Unknown'],
            ['Total Pages', metadata.totalPages || 'Unknown'],
            ['Processing Time (ms)', metadata.processingTime || 'Unknown'],
            ['Extraction Date', new Date().toISOString()],
            ['Tables Detected', extractedData.tables?.length || 0],
            ['Text Items Extracted', extractedData.text?.length || 0]
        ];
        metadataItems.forEach((item, index) => {
            const row = index + 1;
            worksheet.getCell(row, 1).value = item[0];
            worksheet.getCell(row, 2).value = item[1];
            worksheet.getCell(row, 1).font = { bold: true };
        });
        worksheet.getColumn(1).width = 20;
        worksheet.getColumn(2).width = 30;
    }
    generateOutputPath(originalFilePath) {
        const dir = path.dirname(originalFilePath);
        const baseName = path.basename(originalFilePath, path.extname(originalFilePath));
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        return path.join(dir, `${baseName}_converted_${timestamp}.xlsx`);
    }
    autoFitColumns(worksheet) {
        worksheet.columns.forEach((column, index) => {
            let maxLength = 10;
            column.eachCell?.({ includeEmpty: false }, (cell) => {
                const cellValue = cell.value?.toString() || '';
                maxLength = Math.max(maxLength, cellValue.length);
            });
            column.width = Math.min(Math.max(maxLength + 2, 10), 50);
        });
    }
    applyWorksheetFormatting(worksheet) {
        worksheet.eachRow((row) => {
            row.eachCell((cell) => {
                if (!cell.font) {
                    cell.font = { name: 'Calibri', size: 11 };
                }
            });
        });
        worksheet.views = [{ state: 'frozen', ySplit: 1 }];
    }
    parseNumericValue(value) {
        if (!value || typeof value !== 'string')
            return null;
        const cleaned = value.replace(/[,\s$€%]/g, '');
        const parsed = parseFloat(cleaned);
        return !isNaN(parsed) && isFinite(parsed) ? parsed : null;
    }
    groupTextByPages(textItems) {
        const pages = [];
        textItems.forEach(item => {
            const pageIndex = (item.page || 1) - 1;
            if (!pages[pageIndex]) {
                pages[pageIndex] = [];
            }
            pages[pageIndex].push(item);
        });
        return pages;
    }
}
exports.ExcelGeneratorService = ExcelGeneratorService;
//# sourceMappingURL=ExcelGeneratorService.js.map