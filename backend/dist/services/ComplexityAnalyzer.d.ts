import { PDFDocument } from 'pdf-lib';
export interface ComplexityMetrics {
    pageCount: number;
    textDensity: number;
    imageCount: number;
    tableCount: number;
    fontVariations: number;
    layoutComplexity: number;
    hasScannedContent: boolean;
    hasFormFields: boolean;
    hasAnnotations: boolean;
    fileSize: number;
}
export interface ComplexityResult {
    level: 'simple' | 'medium' | 'complex';
    score: number;
    metrics: ComplexityMetrics;
    recommendations: string[];
    estimatedProcessingTime: number;
}
export declare class ComplexityAnalyzer {
    analyze(pdfDoc: PDFDocument, pdfBuffer: Buffer): Promise<ComplexityResult>;
    private extractMetrics;
    private calculateComplexityScore;
    private determineComplexityLevel;
    private generateRecommendations;
    private estimateProcessingTime;
    private estimateTextDensity;
    private estimateImageCount;
    private estimateTableCount;
    private estimateLayoutComplexity;
    private detectScannedContent;
    private getDefaultMetrics;
}
//# sourceMappingURL=ComplexityAnalyzer.d.ts.map