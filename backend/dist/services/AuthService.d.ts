export interface RegisterData {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
}
export interface LoginResult {
    user: {
        id: string;
        email: string;
        firstName?: string;
        lastName?: string;
        subscriptionPlan: string;
        isVerified: boolean;
    };
    tokens: {
        accessToken: string;
        refreshToken: string;
    };
}
export declare class AuthService {
    private readonly JWT_SECRET;
    private readonly JWT_REFRESH_SECRET;
    private readonly ACCESS_TOKEN_EXPIRY;
    private readonly REFRESH_TOKEN_EXPIRY;
    register(data: RegisterData): Promise<LoginResult>;
    login(email: string, password: string): Promise<LoginResult>;
    refreshToken(refreshToken: string): Promise<{
        tokens: {
            accessToken: string;
            refreshToken: string;
        };
    }>;
    logout(refreshToken: string): Promise<void>;
    verifyEmail(token: string): Promise<void>;
    forgotPassword(email: string): Promise<void>;
    resetPassword(token: string, newPassword: string): Promise<void>;
    private generateTokens;
    verifyAccessToken(token: string): {
        userId: string;
    };
}
//# sourceMappingURL=AuthService.d.ts.map