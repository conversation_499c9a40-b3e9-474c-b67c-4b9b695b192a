import { PDFDocument } from 'pdf-lib';
export interface ExtractedText {
    content: string;
    page: number;
    position: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    fontSize: number;
    fontName: string;
}
export interface TableData {
    headers: string[];
    rows: string[][];
    position: {
        page: number;
        x: number;
        y: number;
    };
    confidence: number;
}
export interface ExtractionResult {
    text: ExtractedText[];
    tables: TableData[];
    metadata: {
        totalPages: number;
        extractionMethod: string;
        processingTime: number;
    };
}
export declare class TextExtractionService {
    constructor();
    extractText(pdfDoc: PDFDocument, pdfBuffer: Buffer): Promise<ExtractionResult>;
    private extractWithPDFJS;
    detectTables(extractionResult: any): Promise<TableData[]>;
    private groupTextByPages;
    private detectTablesInPage;
    private groupTextIntoRows;
    private analyzeRowsForTable;
    private calculateTableConfidence;
    private calculateRowAlignment;
    private calculateNumericContent;
    private isNumericContent;
    private fallbackExtraction;
}
//# sourceMappingURL=TextExtractionService.d.ts.map