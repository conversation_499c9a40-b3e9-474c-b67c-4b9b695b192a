"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const database_1 = require("../config/database");
const security_1 = require("../config/security");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
class UserService {
    async getProfile(userId) {
        const user = await (0, database_1.db)('users').where({ id: userId }).first();
        if (!user) {
            throw new errorHandler_1.CustomError('User not found', 404, true, 'USER_NOT_FOUND');
        }
        return {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            subscriptionPlan: user.subscription_plan,
            isVerified: user.is_verified,
            createdAt: user.created_at
        };
    }
    async updateProfile(userId, data) {
        const updateData = {};
        if (data.firstName !== undefined) {
            updateData.first_name = data.firstName;
        }
        if (data.lastName !== undefined) {
            updateData.last_name = data.lastName;
        }
        if (data.email !== undefined) {
            const existingUser = await (0, database_1.db)('users')
                .where({ email: data.email })
                .whereNot({ id: userId })
                .first();
            if (existingUser) {
                throw new errorHandler_1.CustomError('Email already in use', 409, true, 'EMAIL_EXISTS');
            }
            updateData.email = data.email;
            updateData.is_verified = false;
            updateData.verification_token = security_1.EncryptionService.generateSecureToken();
        }
        const [updatedUser] = await (0, database_1.db)('users')
            .where({ id: userId })
            .update(updateData)
            .returning(['id', 'email', 'first_name', 'last_name', 'subscription_plan', 'is_verified', 'created_at']);
        if (!updatedUser) {
            throw new errorHandler_1.CustomError('User not found', 404, true, 'USER_NOT_FOUND');
        }
        logger_1.logger.info(`Profile updated for user: ${updatedUser.email}`);
        return {
            id: updatedUser.id,
            email: updatedUser.email,
            firstName: updatedUser.first_name,
            lastName: updatedUser.last_name,
            subscriptionPlan: updatedUser.subscription_plan,
            isVerified: updatedUser.is_verified,
            createdAt: updatedUser.created_at
        };
    }
    async getUsageStats(userId) {
        const currentMonth = new Date().toISOString().slice(0, 7);
        const currentMonthUsage = await (0, database_1.db)('usage_tracking')
            .where({ user_id: userId, month: currentMonth })
            .first();
        const allTimeUsage = await (0, database_1.db)('usage_tracking')
            .where({ user_id: userId })
            .sum('conversions_count as totalConversions')
            .sum('total_file_size as totalFileSize')
            .sum('total_processing_time as totalProcessingTime')
            .first();
        const user = await (0, database_1.db)('users').where({ id: userId }).first();
        const limits = this.getSubscriptionLimits(user?.subscription_plan || 'free');
        return {
            currentMonth: {
                conversions: currentMonthUsage?.conversions_count || 0,
                totalFileSize: currentMonthUsage?.total_file_size || 0,
                totalProcessingTime: currentMonthUsage?.total_processing_time || 0
            },
            allTime: {
                conversions: parseInt(allTimeUsage?.totalConversions || '0'),
                totalFileSize: parseInt(allTimeUsage?.totalFileSize || '0'),
                totalProcessingTime: parseInt(allTimeUsage?.totalProcessingTime || '0')
            },
            limits
        };
    }
    async getSubscription(userId) {
        const subscription = await (0, database_1.db)('subscriptions')
            .where({ user_id: userId })
            .orderBy('created_at', 'desc')
            .first();
        if (!subscription) {
            return {
                plan: 'free',
                status: 'active'
            };
        }
        return {
            plan: subscription.plan,
            status: subscription.status,
            currentPeriodStart: subscription.current_period_start,
            currentPeriodEnd: subscription.current_period_end,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            stripeCustomerId: subscription.stripe_customer_id,
            stripeSubscriptionId: subscription.stripe_subscription_id
        };
    }
    async deleteAccount(userId, password) {
        const user = await (0, database_1.db)('users').where({ id: userId }).first();
        if (!user) {
            throw new errorHandler_1.CustomError('User not found', 404, true, 'USER_NOT_FOUND');
        }
        const isPasswordValid = await security_1.EncryptionService.comparePassword(password, user.password_hash);
        if (!isPasswordValid) {
            throw new errorHandler_1.CustomError('Invalid password', 401, true, 'INVALID_PASSWORD');
        }
        await database_1.db.transaction(async (trx) => {
            await trx('usage_tracking').where({ user_id: userId }).del();
            await trx('conversions').where({ user_id: userId }).del();
            await trx('subscriptions').where({ user_id: userId }).del();
            await trx('users').where({ id: userId }).del();
        });
        logger_1.logger.info(`Account deleted for user: ${user.email}`);
    }
    async trackUsage(userId, fileSize, processingTime) {
        const month = new Date().toISOString().slice(0, 7);
        await (0, database_1.db)('usage_tracking')
            .insert({
            user_id: userId,
            month,
            conversions_count: 1,
            total_file_size: fileSize,
            total_processing_time: processingTime
        })
            .onConflict(['user_id', 'month'])
            .merge({
            conversions_count: database_1.db.raw('usage_tracking.conversions_count + 1'),
            total_file_size: database_1.db.raw('usage_tracking.total_file_size + ?', [fileSize]),
            total_processing_time: database_1.db.raw('usage_tracking.total_processing_time + ?', [processingTime])
        });
    }
    getSubscriptionLimits(plan) {
        const limits = {
            free: {
                conversions: 3,
                fileSize: 50 * 1024 * 1024
            },
            pro: {
                conversions: Infinity,
                fileSize: 200 * 1024 * 1024
            },
            business: {
                conversions: Infinity,
                fileSize: 500 * 1024 * 1024
            }
        };
        return limits[plan] || limits.free;
    }
}
exports.UserService = UserService;
//# sourceMappingURL=UserService.js.map