import { ProcessingOptions } from './PDFProcessingService';
export interface ExcelGenerationOptions {
    includeMetadata?: boolean;
    autoFitColumns?: boolean;
    addHeaders?: boolean;
    formatNumbers?: boolean;
    addSummary?: boolean;
}
export declare class ExcelGeneratorService {
    generateExcel(extractedData: any, originalFilePath: string, options: ProcessingOptions): Promise<string>;
    private setWorkbookProperties;
    private addDataWorksheet;
    private addTablesToWorksheet;
    private addTextToWorksheet;
    private addMetadataWorksheet;
    private generateOutputPath;
    private autoFitColumns;
    private applyWorksheetFormatting;
    private parseNumericValue;
    private groupTextByPages;
}
//# sourceMappingURL=ExcelGeneratorService.d.ts.map