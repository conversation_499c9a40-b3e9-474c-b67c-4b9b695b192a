export interface ConversionOptions {
    template?: string;
    outputFormat?: 'xlsx' | 'csv';
    extractTables?: boolean;
    extractText?: boolean;
    ocrEnabled?: boolean;
}
export interface ConversionData {
    userId: string;
    file: Express.Multer.File;
    options: ConversionOptions;
}
export interface ConversionResult {
    id: string;
    fileName: string;
    fileSize: number;
    status: string;
    createdAt: Date;
    processingTime?: number;
    resultPath?: string;
}
export interface ConversionsResponse {
    conversions: ConversionResult[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export declare class ConversionService {
    private userService;
    private pdfProcessor;
    startConversion(data: ConversionData): Promise<ConversionResult>;
    getConversions(userId: string, filters: {
        page: number;
        limit: number;
        status?: string;
    }): Promise<ConversionsResponse>;
    getConversion(conversionId: string, userId: string): Promise<ConversionResult>;
    getDownloadPath(conversionId: string, userId: string): Promise<string>;
    deleteConversion(conversionId: string, userId: string): Promise<void>;
    retryConversion(conversionId: string, userId: string): Promise<ConversionResult>;
    private processConversionAsync;
}
//# sourceMappingURL=ConversionService.d.ts.map