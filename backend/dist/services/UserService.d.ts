export interface UpdateProfileData {
    firstName?: string;
    lastName?: string;
    email?: string;
}
export interface UserProfile {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    subscriptionPlan: string;
    isVerified: boolean;
    createdAt: Date;
}
export interface UsageStats {
    currentMonth: {
        conversions: number;
        totalFileSize: number;
        totalProcessingTime: number;
    };
    allTime: {
        conversions: number;
        totalFileSize: number;
        totalProcessingTime: number;
    };
    limits: {
        conversions: number;
        fileSize: number;
    };
}
export interface SubscriptionDetails {
    plan: string;
    status: string;
    currentPeriodStart?: Date;
    currentPeriodEnd?: Date;
    cancelAtPeriodEnd?: boolean;
    stripeCustomerId?: string;
    stripeSubscriptionId?: string;
}
export declare class UserService {
    getProfile(userId: string): Promise<UserProfile>;
    updateProfile(userId: string, data: UpdateProfileData): Promise<UserProfile>;
    getUsageStats(userId: string): Promise<UsageStats>;
    getSubscription(userId: string): Promise<SubscriptionDetails>;
    deleteAccount(userId: string, password: string): Promise<void>;
    trackUsage(userId: string, fileSize: number, processingTime: number): Promise<void>;
    private getSubscriptionLimits;
}
//# sourceMappingURL=UserService.d.ts.map