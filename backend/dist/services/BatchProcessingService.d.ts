import { ProcessingOptions } from './PDFProcessingService';
export interface BatchJob {
    id: string;
    userId: string;
    name: string;
    files: BatchFile[];
    options: ProcessingOptions;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    progress: {
        total: number;
        completed: number;
        failed: number;
        currentFile?: string;
    };
    createdAt: Date;
    startedAt?: Date;
    completedAt?: Date;
    results?: BatchResult[];
    error?: string;
}
export interface BatchFile {
    id: string;
    originalName: string;
    filePath: string;
    fileSize: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    processingTime?: number;
    outputPath?: string;
    error?: string;
}
export interface BatchResult {
    fileId: string;
    fileName: string;
    success: boolean;
    outputPath?: string;
    processingTime: number;
    complexity?: string;
    error?: string;
}
export interface BatchOptions {
    maxConcurrent?: number;
    priority?: 'low' | 'normal' | 'high';
    notifyOnComplete?: boolean;
    retryFailures?: boolean;
    maxRetries?: number;
}
export declare class BatchProcessingService {
    private pdfProcessor;
    private activeJobs;
    private readonly maxConcurrentJobs;
    private readonly maxConcurrentFiles;
    constructor();
    createBatchJob(userId: string, files: Express.Multer.File[], options: ProcessingOptions, batchOptions?: BatchOptions): Promise<BatchJob>;
    getBatchJob(jobId: string, userId: string): Promise<BatchJob | null>;
    getUserBatchJobs(userId: string, page?: number, limit?: number): Promise<{
        jobs: BatchJob[];
        total: number;
    }>;
    cancelBatchJob(jobId: string, userId: string): Promise<boolean>;
    private processBatchJobAsync;
    private processFileInBatch;
    private saveBatchJobToDatabase;
    private updateBatchJobInDatabase;
    private getBatchJobFromDatabase;
    private mapDatabaseToBatchJob;
}
//# sourceMappingURL=BatchProcessingService.d.ts.map