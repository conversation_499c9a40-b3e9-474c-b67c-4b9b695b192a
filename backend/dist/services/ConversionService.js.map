{"version": 3, "file": "ConversionService.js", "sourceRoot": "", "sources": ["../../src/services/ConversionService.ts"], "names": [], "mappings": ";;;;;;AAAA,+BAAoC;AACpC,2DAA6B;AAC7B,gDAAwB;AACxB,iDAAwC;AACxC,6DAAyD;AACzD,+CAA4C;AAC5C,4CAAyC;AAoCzC,MAAa,iBAAiB;IAA9B;QACU,gBAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IAuQ1C,CAAC;IAlQC,KAAK,CAAC,eAAe,CAAC,IAAoB;QACxC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAGvC,MAAM,YAAY,GAAG,IAAA,SAAM,GAAE,CAAC;QAC9B,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;aACzC,MAAM,CAAC;YACN,EAAE,EAAE,YAAY;YAChB,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,SAAS,EAAE,IAAI,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI,CAAC,IAAI;YACpB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;SAClC,CAAC;aACD,SAAS,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;QAEvE,eAAM,CAAC,IAAI,CAAC,uBAAuB,YAAY,cAAc,MAAM,EAAE,CAAC,CAAC;QAGvE,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEzD,OAAO;YACL,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,QAAQ,EAAE,UAAU,CAAC,SAAS;YAC9B,QAAQ,EAAE,UAAU,CAAC,SAAS;YAC9B,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,SAAS,EAAE,UAAU,CAAC,UAAU;SACjC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,OAIpC;QACC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QACxC,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAElC,IAAI,KAAK,GAAG,IAAA,aAAE,EAAC,aAAa,CAAC;aAC1B,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;aAC1B,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAEjC,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAClC,CAAC;QAGD,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACjC,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QACzD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAe,CAAC,CAAC;QAGxC,MAAM,WAAW,GAAG,MAAM,KAAK;aAC5B,KAAK,CAAC,KAAK,CAAC;aACZ,MAAM,CAAC,MAAM,CAAC;aACd,MAAM,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC,CAAC;QAEtG,OAAO;YACL,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACjC,EAAE,EAAE,CAAC,CAAC,EAAE;gBACR,QAAQ,EAAE,CAAC,CAAC,SAAS;gBACrB,QAAQ,EAAE,CAAC,CAAC,SAAS;gBACrB,MAAM,EAAE,CAAC,CAAC,MAAM;gBAChB,SAAS,EAAE,CAAC,CAAC,UAAU;gBACvB,cAAc,EAAE,CAAC,CAAC,eAAe;gBACjC,UAAU,EAAE,CAAC,CAAC,WAAW;aAC1B,CAAC,CAAC;YACH,UAAU,EAAE;gBACV,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC;SACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,YAAoB,EAAE,MAAc;QACtD,MAAM,UAAU,GAAG,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;aACvC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;aAC5C,KAAK,EAAE,CAAC;QAEX,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAW,CAAC,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC;QACnF,CAAC;QAED,OAAO;YACL,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,QAAQ,EAAE,UAAU,CAAC,SAAS;YAC9B,QAAQ,EAAE,UAAU,CAAC,SAAS;YAC9B,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,SAAS,EAAE,UAAU,CAAC,UAAU;YAChC,cAAc,EAAE,UAAU,CAAC,eAAe;YAC1C,UAAU,EAAE,UAAU,CAAC,WAAW;SACnC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,YAAoB,EAAE,MAAc;QACxD,MAAM,UAAU,GAAG,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;aACvC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;aAC5C,KAAK,EAAE,CAAC;QAEX,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAW,CAAC,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,IAAI,0BAAW,CAAC,gDAAgD,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACvG,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YACxC,OAAO,UAAU,CAAC,WAAW,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,0BAAW,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,YAAoB,EAAE,MAAc;QACzD,MAAM,UAAU,GAAG,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;aACvC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;aAC5C,KAAK,EAAE,CAAC;QAEX,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAW,CAAC,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC;QACnF,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,kBAAE,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtC,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC3B,MAAM,kBAAE,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yCAAyC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/E,CAAC;QAGD,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QAE1D,eAAM,CAAC,IAAI,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;IACrD,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,YAAoB,EAAE,MAAc;QACxD,MAAM,UAAU,GAAG,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;aACvC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;aAC5C,KAAK,EAAE,CAAC;QAEX,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAW,CAAC,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAI,0BAAW,CAAC,wCAAwC,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;QAC/F,CAAC;QAGD,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;aACpB,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;aAC3B,MAAM,CAAC;YACN,MAAM,EAAE,SAAS;YACjB,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAGL,MAAM,IAAI,GAAG;YACX,YAAY,EAAE,UAAU,CAAC,SAAS;YAClC,IAAI,EAAE,UAAU,CAAC,SAAS;YAC1B,IAAI,EAAE,UAAU,CAAC,SAAS;SACJ,CAAC;QAEzB,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3E,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEzD,eAAM,CAAC,IAAI,CAAC,6BAA6B,YAAY,EAAE,CAAC,CAAC;QAEzD,OAAO;YACL,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,QAAQ,EAAE,UAAU,CAAC,SAAS;YAC9B,QAAQ,EAAE,UAAU,CAAC,SAAS;YAC9B,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,UAAU,CAAC,UAAU;SACjC,CAAC;IACJ,CAAC;IAKO,KAAK,CAAC,sBAAsB,CAAC,YAAoB,EAAE,IAAyB,EAAE,OAA0B;QAC9G,IAAI,CAAC;YAEH,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;iBACpB,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;iBAC3B,MAAM,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;YAGpC,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;YACnD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;YAGlE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAC1B,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EACvB,UAAU,YAAY,OAAO,CAC9B,CAAC;YAGF,MAAM,gBAAgB,GAAG,4DAA4D,CAAC;YACtF,MAAM,kBAAE,CAAC,SAAS,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;YAGjD,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;iBACpB,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;iBAC3B,MAAM,CAAC;gBACN,MAAM,EAAE,WAAW;gBACnB,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;gBAC3C,WAAW,EAAE,UAAU;aACxB,CAAC,CAAC;YAGL,MAAM,UAAU,GAAG,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAC/E,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAC/B,UAAU,CAAC,OAAO,EAClB,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAC3B,CAAC;YACJ,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,yBAAyB,YAAY,EAAE,CAAC,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,YAAY,EAAE,EAAE,KAAK,CAAC,CAAC;YAG1D,MAAM,IAAA,aAAE,EAAC,aAAa,CAAC;iBACpB,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;iBAC3B,MAAM,CAAC;gBACN,MAAM,EAAE,QAAQ;gBAChB,aAAa,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aACxE,CAAC,CAAC;QACP,CAAC;IACH,CAAC;CACF;AAxQD,8CAwQC"}