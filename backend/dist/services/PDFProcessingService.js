"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PDFProcessingService = void 0;
const pdf_lib_1 = require("pdf-lib");
const fs = __importStar(require("fs/promises"));
const logger_1 = require("../utils/logger");
const ExcelGeneratorService_1 = require("./ExcelGeneratorService");
const ComplexityAnalyzer_1 = require("./ComplexityAnalyzer");
const TextExtractionService_1 = require("./TextExtractionService");
class PDFProcessingService {
    constructor() {
        this.excelGenerator = new ExcelGeneratorService_1.ExcelGeneratorService();
        this.complexityAnalyzer = new ComplexityAnalyzer_1.ComplexityAnalyzer();
        this.textExtractor = new TextExtractionService_1.TextExtractionService();
    }
    async processPDF(filePath, options, progressCallback) {
        const startTime = Date.now();
        try {
            logger_1.logger.info(`Starting PDF processing: ${filePath}`);
            progressCallback?.({
                stage: 'analyzing',
                progress: 10,
                message: 'Analyzing document complexity...'
            });
            const pdfBuffer = await fs.readFile(filePath);
            const pdfDoc = await pdf_lib_1.PDFDocument.load(pdfBuffer);
            const complexity = await this.complexityAnalyzer.analyze(pdfDoc, pdfBuffer);
            logger_1.logger.info(`Document complexity: ${complexity.level}`);
            const processingMethod = this.selectProcessingMethod(complexity);
            progressCallback?.({
                stage: 'extracting',
                progress: 30,
                message: `Extracting data using ${processingMethod} processing...`
            });
            let extractedData;
            if (processingMethod === 'local') {
                extractedData = await this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
            }
            else if (processingMethod === 'cloud') {
                extractedData = await this.processWithCloud(pdfBuffer, options, progressCallback);
            }
            else {
                extractedData = await this.processHybrid(pdfDoc, pdfBuffer, options, progressCallback);
            }
            progressCallback?.({
                stage: 'generating',
                progress: 80,
                message: 'Generating Excel file...'
            });
            const outputPath = await this.excelGenerator.generateExcel(extractedData, filePath, options);
            progressCallback?.({
                stage: 'completed',
                progress: 100,
                message: 'Processing completed successfully!'
            });
            const processingTime = Date.now() - startTime;
            return {
                success: true,
                outputPath,
                extractedData,
                processingTime,
                complexity: complexity.level,
                method: processingMethod
            };
        }
        catch (error) {
            logger_1.logger.error('PDF processing failed:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            progressCallback?.({
                stage: 'failed',
                progress: 0,
                message: `Processing failed: ${errorMessage}`
            });
            return {
                success: false,
                processingTime: Date.now() - startTime,
                complexity: 'simple',
                method: 'local',
                error: errorMessage
            };
        }
    }
    selectProcessingMethod(complexity) {
        if (complexity.level === 'simple') {
            return 'local';
        }
        else if (complexity.level === 'medium') {
            return 'local';
        }
        else {
            return 'local';
        }
    }
    async processLocally(pdfDoc, pdfBuffer, options, progressCallback) {
        progressCallback?.({
            stage: 'processing',
            progress: 40,
            message: 'Extracting text content...'
        });
        const textData = await this.textExtractor.extractText(pdfDoc, pdfBuffer);
        progressCallback?.({
            stage: 'processing',
            progress: 60,
            message: 'Detecting tables and structure...'
        });
        const structuredData = await this.textExtractor.detectTables(textData);
        progressCallback?.({
            stage: 'processing',
            progress: 70,
            message: 'Processing extracted data...'
        });
        const processedData = this.processExtractedData(structuredData, options);
        return processedData;
    }
    async processWithCloud(pdfBuffer, options, progressCallback) {
        throw new Error('Cloud processing not yet implemented');
    }
    async processHybrid(pdfDoc, pdfBuffer, options, progressCallback) {
        return this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
    }
    processExtractedData(rawData, options) {
        const processedData = {
            tables: [],
            text: [],
            metadata: {
                extractionMethod: 'local',
                timestamp: new Date().toISOString(),
                options
            }
        };
        if (rawData.tables && rawData.tables.length > 0) {
            processedData.tables = rawData.tables.map((table) => ({
                headers: table.headers || [],
                rows: table.rows || [],
                position: table.position || { page: 1, x: 0, y: 0 }
            }));
        }
        if (rawData.text) {
            processedData.text = Array.isArray(rawData.text) ? rawData.text : [rawData.text];
        }
        return processedData;
    }
    static getSupportedFormats() {
        return ['pdf'];
    }
    static async validatePDF(filePath) {
        try {
            const buffer = await fs.readFile(filePath);
            await pdf_lib_1.PDFDocument.load(buffer);
            return true;
        }
        catch (error) {
            logger_1.logger.error('PDF validation failed:', error);
            return false;
        }
    }
}
exports.PDFProcessingService = PDFProcessingService;
//# sourceMappingURL=PDFProcessingService.js.map