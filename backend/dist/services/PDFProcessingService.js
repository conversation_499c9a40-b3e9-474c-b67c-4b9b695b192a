"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PDFProcessingService = void 0;
const pdf_lib_1 = require("pdf-lib");
const fs = __importStar(require("fs/promises"));
const logger_1 = require("../utils/logger");
const ExcelGeneratorService_1 = require("./ExcelGeneratorService");
const ComplexityAnalyzer_1 = require("./ComplexityAnalyzer");
const TextExtractionService_1 = require("./TextExtractionService");
const OCRService_1 = require("./OCRService");
const CacheService_1 = require("./CacheService");
class PDFProcessingService {
    constructor() {
        this.excelGenerator = new ExcelGeneratorService_1.ExcelGeneratorService();
        this.complexityAnalyzer = new ComplexityAnalyzer_1.ComplexityAnalyzer();
        this.textExtractor = new TextExtractionService_1.TextExtractionService();
        this.ocrService = new OCRService_1.OCRService();
        this.cacheService = new CacheService_1.CacheService();
    }
    async processPDF(filePath, options, progressCallback) {
        const startTime = Date.now();
        try {
            logger_1.logger.info(`Starting PDF processing: ${filePath}`);
            const cachedResult = await this.cacheService.getCachedProcessingResult(filePath, options);
            if (cachedResult) {
                logger_1.logger.info('Using cached processing result');
                progressCallback?.({
                    stage: 'completed',
                    progress: 100,
                    message: 'Using cached result - processing completed instantly!'
                });
                return {
                    success: true,
                    outputPath: await this.regenerateExcelFromCache(cachedResult, filePath, options),
                    extractedData: cachedResult.extractedData,
                    processingTime: 100,
                    complexity: cachedResult.complexity?.level || 'simple',
                    method: 'local'
                };
            }
            progressCallback?.({
                stage: 'analyzing',
                progress: 10,
                message: 'Analyzing document complexity...'
            });
            const pdfBuffer = await fs.readFile(filePath);
            const pdfDoc = await pdf_lib_1.PDFDocument.load(pdfBuffer);
            let complexity = await this.cacheService.getCachedComplexityAnalysis(filePath);
            if (!complexity) {
                complexity = await this.complexityAnalyzer.analyze(pdfDoc, pdfBuffer);
                await this.cacheService.cacheComplexityAnalysis(filePath, complexity);
            }
            logger_1.logger.info(`Document complexity: ${complexity.level}`);
            const processingMethod = this.selectProcessingMethod(complexity);
            progressCallback?.({
                stage: 'extracting',
                progress: 30,
                message: `Extracting data using ${processingMethod} processing...`
            });
            let extractedData;
            const isScanned = await OCRService_1.OCRService.isScannedDocument(filePath);
            if (isScanned && options.ocrEnabled) {
                progressCallback?.({
                    stage: 'extracting',
                    progress: 35,
                    message: 'Scanned document detected - using OCR processing...'
                });
                extractedData = await this.processWithOCR(filePath, options, progressCallback);
            }
            else {
                if (processingMethod === 'local') {
                    extractedData = await this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
                }
                else if (processingMethod === 'cloud') {
                    extractedData = await this.processWithCloud(pdfBuffer, options, progressCallback);
                }
                else {
                    extractedData = await this.processHybrid(pdfDoc, pdfBuffer, options, progressCallback);
                }
            }
            progressCallback?.({
                stage: 'generating',
                progress: 80,
                message: 'Generating Excel file...'
            });
            const outputPath = await this.excelGenerator.generateExcel(extractedData, filePath, options);
            progressCallback?.({
                stage: 'completed',
                progress: 100,
                message: 'Processing completed successfully!'
            });
            const processingTime = Date.now() - startTime;
            const result = {
                success: true,
                outputPath,
                extractedData,
                processingTime,
                complexity: complexity.level,
                method: processingMethod
            };
            await this.cacheService.cacheProcessingResult(filePath, options, result);
            return result;
        }
        catch (error) {
            logger_1.logger.error('PDF processing failed:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            progressCallback?.({
                stage: 'failed',
                progress: 0,
                message: `Processing failed: ${errorMessage}`
            });
            return {
                success: false,
                processingTime: Date.now() - startTime,
                complexity: 'simple',
                method: 'local',
                error: errorMessage
            };
        }
    }
    selectProcessingMethod(complexity) {
        if (complexity.level === 'simple') {
            return 'local';
        }
        else if (complexity.level === 'medium') {
            return 'local';
        }
        else {
            return 'local';
        }
    }
    async processLocally(pdfDoc, pdfBuffer, options, progressCallback) {
        progressCallback?.({
            stage: 'processing',
            progress: 40,
            message: 'Extracting text content...'
        });
        const textData = await this.textExtractor.extractText(pdfDoc, pdfBuffer);
        progressCallback?.({
            stage: 'processing',
            progress: 60,
            message: 'Detecting tables and structure...'
        });
        const structuredData = await this.textExtractor.detectTables(textData);
        progressCallback?.({
            stage: 'processing',
            progress: 70,
            message: 'Processing extracted data...'
        });
        const processedData = this.processExtractedData(structuredData, options);
        return processedData;
    }
    async processWithCloud(pdfBuffer, options, progressCallback) {
        throw new Error('Cloud processing not yet implemented');
    }
    async processHybrid(pdfDoc, pdfBuffer, options, progressCallback) {
        return this.processLocally(pdfDoc, pdfBuffer, options, progressCallback);
    }
    processExtractedData(rawData, options) {
        const processedData = {
            tables: [],
            text: [],
            metadata: {
                extractionMethod: 'local',
                timestamp: new Date().toISOString(),
                options
            }
        };
        if (rawData.tables && rawData.tables.length > 0) {
            processedData.tables = rawData.tables.map((table) => ({
                headers: table.headers || [],
                rows: table.rows || [],
                position: table.position || { page: 1, x: 0, y: 0 }
            }));
        }
        if (rawData.text) {
            processedData.text = Array.isArray(rawData.text) ? rawData.text : [rawData.text];
        }
        return processedData;
    }
    async processWithOCR(filePath, options, progressCallback) {
        try {
            progressCallback?.({
                stage: 'processing',
                progress: 40,
                message: 'Running OCR on scanned document...'
            });
            const ocrResults = await this.ocrService.processPDFWithOCR(filePath, {
                language: 'eng+pol',
                preprocessImage: true,
                dpi: 300
            });
            progressCallback?.({
                stage: 'processing',
                progress: 70,
                message: 'Processing OCR results...'
            });
            const processedData = {
                tables: [],
                text: [],
                metadata: {
                    extractionMethod: 'ocr',
                    timestamp: new Date().toISOString(),
                    options,
                    ocrResults: ocrResults.length
                }
            };
            ocrResults.forEach(pageResult => {
                if (pageResult.result.text) {
                    processedData.text.push({
                        content: pageResult.result.text,
                        page: pageResult.pageNumber,
                        position: { x: 0, y: 0, width: 0, height: 0 },
                        fontSize: 12,
                        fontName: 'ocr-extracted'
                    });
                }
                const lines = pageResult.result.lines;
                if (lines && lines.length > 0) {
                    const tableCandidate = this.detectTablesInOCRText(lines, pageResult.pageNumber);
                    if (tableCandidate) {
                        processedData.tables.push(tableCandidate);
                    }
                }
            });
            return processedData;
        }
        catch (error) {
            logger_1.logger.error('OCR processing failed:', error);
            throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    detectTablesInOCRText(lines, pageNumber) {
        const potentialTableLines = lines.filter(line => {
            const words = line.text.trim().split(/\s+/);
            return words.length >= 2 && words.length <= 10;
        });
        if (potentialTableLines.length < 2) {
            return null;
        }
        const headers = potentialTableLines[0].text.trim().split(/\s+/);
        const rows = potentialTableLines.slice(1).map(line => line.text.trim().split(/\s+/));
        return {
            headers,
            rows,
            position: {
                page: pageNumber,
                x: 0,
                y: 0
            },
            confidence: 0.7
        };
    }
    async regenerateExcelFromCache(cachedResult, originalFilePath, options) {
        try {
            const outputPath = await this.excelGenerator.generateExcel(cachedResult.extractedData, originalFilePath, options);
            return outputPath;
        }
        catch (error) {
            logger_1.logger.error('Failed to regenerate Excel from cache:', error);
            throw error;
        }
    }
    static getSupportedFormats() {
        return ['pdf'];
    }
    static async validatePDF(filePath) {
        try {
            const buffer = await fs.readFile(filePath);
            await pdf_lib_1.PDFDocument.load(buffer);
            return true;
        }
        catch (error) {
            logger_1.logger.error('PDF validation failed:', error);
            return false;
        }
    }
    async cleanup() {
        try {
            await this.ocrService.cleanup();
            await this.cacheService.disconnect();
        }
        catch (error) {
            logger_1.logger.error('Cleanup failed:', error);
        }
    }
}
exports.PDFProcessingService = PDFProcessingService;
//# sourceMappingURL=PDFProcessingService.js.map