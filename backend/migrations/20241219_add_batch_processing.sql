-- Migration: Add batch processing tables
-- Created: 2024-12-19
-- Description: Add support for batch processing of multiple PDF files

-- Create batch_jobs table
CREATE TABLE IF NOT EXISTS batch_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    total_files INTEGER NOT NULL DEFAULT 0,
    completed_files INTEGER NOT NULL DEFAULT 0,
    failed_files INTEGER NOT NULL DEFAULT 0,
    options JSONB,
    files JSONB,
    results JSONB,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for batch_jobs
CREATE INDEX IF NOT EXISTS idx_batch_jobs_user_id ON batch_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_status ON batch_jobs(status);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_created_at ON batch_jobs(created_at DESC);

-- Create processing_cache table for caching results
CREATE TABLE IF NOT EXISTS processing_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_hash VARCHAR(64) NOT NULL UNIQUE,
    complexity_data JSONB,
    extracted_data JSONB,
    processing_time INTEGER,
    cache_version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for processing_cache
CREATE INDEX IF NOT EXISTS idx_processing_cache_file_hash ON processing_cache(file_hash);
CREATE INDEX IF NOT EXISTS idx_processing_cache_expires_at ON processing_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_processing_cache_created_at ON processing_cache(created_at DESC);

-- Create processing_analytics table for tracking performance
CREATE TABLE IF NOT EXISTS processing_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    conversion_id UUID REFERENCES conversions(id) ON DELETE SET NULL,
    batch_job_id UUID REFERENCES batch_jobs(id) ON DELETE SET NULL,
    file_size BIGINT,
    page_count INTEGER,
    complexity_level VARCHAR(20),
    processing_method VARCHAR(20),
    processing_time INTEGER,
    cache_hit BOOLEAN DEFAULT FALSE,
    ocr_used BOOLEAN DEFAULT FALSE,
    tables_detected INTEGER DEFAULT 0,
    text_items_extracted INTEGER DEFAULT 0,
    success BOOLEAN DEFAULT TRUE,
    error_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for processing_analytics
CREATE INDEX IF NOT EXISTS idx_processing_analytics_user_id ON processing_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_processing_analytics_created_at ON processing_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_processing_analytics_complexity ON processing_analytics(complexity_level);
CREATE INDEX IF NOT EXISTS idx_processing_analytics_method ON processing_analytics(processing_method);
CREATE INDEX IF NOT EXISTS idx_processing_analytics_success ON processing_analytics(success);

-- Add new columns to existing conversions table for enhanced tracking
ALTER TABLE conversions 
ADD COLUMN IF NOT EXISTS complexity_level VARCHAR(20),
ADD COLUMN IF NOT EXISTS processing_method VARCHAR(20) DEFAULT 'local',
ADD COLUMN IF NOT EXISTS cache_hit BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS ocr_used BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS tables_detected INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS text_items_extracted INTEGER DEFAULT 0;

-- Create function to automatically clean up expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM processing_cache 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to update cache access statistics
CREATE OR REPLACE FUNCTION update_cache_access(cache_file_hash VARCHAR(64))
RETURNS VOID AS $$
BEGIN
    UPDATE processing_cache 
    SET 
        access_count = access_count + 1,
        last_accessed = CURRENT_TIMESTAMP
    WHERE file_hash = cache_file_hash;
END;
$$ LANGUAGE plpgsql;

-- Create function to get processing statistics
CREATE OR REPLACE FUNCTION get_processing_stats(
    user_id_param UUID DEFAULT NULL,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    total_conversions BIGINT,
    successful_conversions BIGINT,
    failed_conversions BIGINT,
    avg_processing_time NUMERIC,
    cache_hit_rate NUMERIC,
    ocr_usage_rate NUMERIC,
    complexity_distribution JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_conversions,
        COUNT(*) FILTER (WHERE success = TRUE) as successful_conversions,
        COUNT(*) FILTER (WHERE success = FALSE) as failed_conversions,
        ROUND(AVG(processing_time), 2) as avg_processing_time,
        ROUND(
            (COUNT(*) FILTER (WHERE cache_hit = TRUE)::NUMERIC / NULLIF(COUNT(*), 0)) * 100, 
            2
        ) as cache_hit_rate,
        ROUND(
            (COUNT(*) FILTER (WHERE ocr_used = TRUE)::NUMERIC / NULLIF(COUNT(*), 0)) * 100, 
            2
        ) as ocr_usage_rate,
        jsonb_build_object(
            'simple', COUNT(*) FILTER (WHERE complexity_level = 'simple'),
            'medium', COUNT(*) FILTER (WHERE complexity_level = 'medium'),
            'complex', COUNT(*) FILTER (WHERE complexity_level = 'complex')
        ) as complexity_distribution
    FROM processing_analytics
    WHERE 
        (user_id_param IS NULL OR user_id = user_id_param)
        AND created_at >= CURRENT_TIMESTAMP - (days_back || ' days')::INTERVAL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to batch_jobs table
DROP TRIGGER IF EXISTS update_batch_jobs_updated_at ON batch_jobs;
CREATE TRIGGER update_batch_jobs_updated_at
    BEFORE UPDATE ON batch_jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data for testing (optional)
-- This can be removed in production
INSERT INTO processing_cache (file_hash, complexity_data, extracted_data, processing_time)
VALUES (
    'test_hash_123',
    '{"level": "simple", "score": 25}',
    '{"tables": [], "text": [{"content": "Test content", "page": 1}]}',
    2500
) ON CONFLICT (file_hash) DO NOTHING;

-- Create view for batch job summary
CREATE OR REPLACE VIEW batch_jobs_summary AS
SELECT 
    bj.id,
    bj.user_id,
    bj.name,
    bj.status,
    bj.total_files,
    bj.completed_files,
    bj.failed_files,
    ROUND(
        (bj.completed_files::NUMERIC / NULLIF(bj.total_files, 0)) * 100, 
        2
    ) as completion_percentage,
    bj.created_at,
    bj.started_at,
    bj.completed_at,
    CASE 
        WHEN bj.completed_at IS NOT NULL AND bj.started_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (bj.completed_at - bj.started_at))::INTEGER
        ELSE NULL 
    END as total_processing_time_seconds,
    u.email as user_email
FROM batch_jobs bj
LEFT JOIN users u ON bj.user_id = u.id;

-- Grant permissions (adjust as needed for your user roles)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON batch_jobs TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON processing_cache TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON processing_analytics TO your_app_user;
-- GRANT SELECT ON batch_jobs_summary TO your_app_user;

-- Add comments for documentation
COMMENT ON TABLE batch_jobs IS 'Stores batch processing jobs for multiple PDF files';
COMMENT ON TABLE processing_cache IS 'Caches processing results to improve performance';
COMMENT ON TABLE processing_analytics IS 'Tracks processing performance and usage analytics';
COMMENT ON VIEW batch_jobs_summary IS 'Provides summary view of batch jobs with calculated metrics';

COMMENT ON COLUMN batch_jobs.options IS 'JSON object containing processing options';
COMMENT ON COLUMN batch_jobs.files IS 'JSON array containing file information';
COMMENT ON COLUMN batch_jobs.results IS 'JSON array containing processing results';

COMMENT ON COLUMN processing_cache.file_hash IS 'SHA-256 hash of file content and options';
COMMENT ON COLUMN processing_cache.complexity_data IS 'Cached complexity analysis results';
COMMENT ON COLUMN processing_cache.extracted_data IS 'Cached extraction results';

COMMENT ON FUNCTION cleanup_expired_cache() IS 'Removes expired cache entries and returns count of deleted rows';
COMMENT ON FUNCTION update_cache_access(VARCHAR) IS 'Updates access statistics for cache entries';
COMMENT ON FUNCTION get_processing_stats(UUID, INTEGER) IS 'Returns processing statistics for analysis';
