{"code": "ENOENT", "errno": -2, "level": "\u001b[31<PERSON><PERSON>r\u001b[39m", "message": "\u001b[31mFailed to analyze document: ENOENT: no such file or directory, stat './test-files/sample.pdf'\u001b[39m", "path": "./test-files/sample.pdf", "stack": "Error: ENOENT: no such file or directory, stat './test-files/sample.pdf'\n    at async Object.stat (node:internal/fs/promises:1036:18)\n    at async OCRService.isScannedDocument (/home/<USER>/Dokumenty/excel/backend/dist/services/OCRService.js:190:27)\n    at async testPhase4Features (/home/<USER>/Dokumenty/excel/backend/test-phase4-features.js:32:23)\n    at async runAllTests (/home/<USER>/Dokumenty/excel/backend/test-phase4-features.js:126:3)", "syscall": "stat", "timestamp": "2025-06-19 16:29:25:2925"}