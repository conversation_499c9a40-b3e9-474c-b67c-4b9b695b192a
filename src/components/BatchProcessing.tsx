import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Upload, X, Play, Trash2, Download, Stop, Plus, History, FileText, CheckCircle, XCircle, Clock, Layers } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/use-toast';

interface BatchFile {
  id: string;
  name: string;
  size: number;
  file: File;
}

interface ProcessingOptions {
  extractTables: boolean;
  extractText: boolean;
  ocrEnabled: boolean;
  outputFormat: 'xlsx' | 'csv';
}

interface BatchJob {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: {
    total: number;
    completed: number;
    failed: number;
    currentFile?: string;
  };
  createdAt: string;
  results?: BatchResult[];
}

interface BatchResult {
  fileId: string;
  fileName: string;
  success: boolean;
  outputPath?: string;
  processingTime: number;
  complexity?: string;
  error?: string;
}

const BatchProcessing: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<BatchFile[]>([]);
  const [currentBatch, setCurrentBatch] = useState<BatchJob | null>(null);
  const [batchHistory, setBatchHistory] = useState<BatchJob[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [options, setOptions] = useState<ProcessingOptions>({
    extractTables: true,
    extractText: true,
    ocrEnabled: false,
    outputFormat: 'xlsx'
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  // File handling
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    addFiles(files);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    addFiles(files);
  }, []);

  const addFiles = useCallback((files: File[]) => {
    const pdfFiles = files.filter(file => file.type === 'application/pdf');
    
    if (pdfFiles.length !== files.length) {
      toast({
        title: "Warning",
        description: "Only PDF files are allowed",
        variant: "destructive"
      });
    }

    if (selectedFiles.length + pdfFiles.length > 50) {
      toast({
        title: "Error",
        description: "Maximum 50 files allowed per batch",
        variant: "destructive"
      });
      return;
    }

    const newFiles: BatchFile[] = pdfFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      file
    }));

    setSelectedFiles(prev => [...prev, ...newFiles]);
  }, [selectedFiles.length, toast]);

  const removeFile = useCallback((id: string) => {
    setSelectedFiles(prev => prev.filter(file => file.id !== id));
  }, []);

  const clearFiles = useCallback(() => {
    setSelectedFiles([]);
  }, []);

  // Batch processing
  const startBatchProcessing = async () => {
    if (selectedFiles.length === 0) return;

    setIsProcessing(true);

    try {
      const formData = new FormData();
      
      selectedFiles.forEach(file => {
        formData.append('files', file.file);
      });

      Object.entries(options).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });

      const response = await fetch('/api/v1/batch/create', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.token}`
        },
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Success",
          description: "Batch processing started successfully"
        });

        await loadBatch(result.data.jobId);
        startPolling();
        setSelectedFiles([]);
      } else {
        throw new Error(result.error || 'Failed to start batch processing');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to start batch processing",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const loadBatch = async (batchId: string) => {
    try {
      const response = await fetch(`/api/v1/batch/${batchId}`, {
        headers: {
          'Authorization': `Bearer ${user?.token}`
        }
      });

      const result = await response.json();

      if (result.success) {
        setCurrentBatch(result.data);
      }
    } catch (error) {
      console.error('Failed to load batch:', error);
    }
  };

  const startPolling = () => {
    if (pollIntervalRef.current) clearInterval(pollIntervalRef.current);
    
    pollIntervalRef.current = setInterval(async () => {
      if (currentBatch && ['processing', 'pending'].includes(currentBatch.status)) {
        await loadBatch(currentBatch.id);
      } else {
        stopPolling();
      }
    }, 2000);
  };

  const stopPolling = () => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }
  };

  const cancelBatch = async () => {
    if (!currentBatch) return;

    try {
      const response = await fetch(`/api/v1/batch/${currentBatch.id}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.token}`
        }
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Info",
          description: "Batch processing cancelled"
        });
        await loadBatch(currentBatch.id);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel batch",
        variant: "destructive"
      });
    }
  };

  const startNewBatch = () => {
    setCurrentBatch(null);
    stopPolling();
  };

  // Utility functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatProcessingTime = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getProgressPercentage = (): number => {
    if (!currentBatch) return 0;
    const { completed, total } = currentBatch.progress;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  const getStatusColor = (status: string): string => {
    const colors = {
      pending: 'text-yellow-600',
      processing: 'text-blue-600',
      completed: 'text-green-600',
      failed: 'text-red-600',
      cancelled: 'text-gray-600'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600';
  };

  // Load batch history on mount
  useEffect(() => {
    const loadBatchHistory = async () => {
      try {
        const response = await fetch('/api/v1/batch?limit=10', {
          headers: {
            'Authorization': `Bearer ${user?.token}`
          }
        });

        const result = await response.json();

        if (result.success) {
          setBatchHistory(result.data.jobs);
        }
      } catch (error) {
        console.error('Failed to load batch history:', error);
      }
    };

    if (user?.token) {
      loadBatchHistory();
    }
  }, [user?.token]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4 flex items-center justify-center gap-3">
          <Layers className="w-8 h-8 text-blue-600" />
          Batch Processing
        </h2>
        <p className="text-lg text-gray-600">
          Process multiple PDF files simultaneously for maximum efficiency
        </p>
      </div>

      {!currentBatch ? (
        /* Upload Section */
        <div className="space-y-6">
          {/* Drop Zone */}
          <div
            className={`border-3 border-dashed rounded-xl p-12 text-center transition-all duration-300 ${
              isDragOver 
                ? 'border-blue-400 bg-blue-50' 
                : 'border-gray-300 bg-gray-50 hover:border-gray-400'
            }`}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault();
              setIsDragOver(true);
            }}
            onDragLeave={() => setIsDragOver(false)}
          >
            <Upload className="w-16 h-16 text-blue-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">
              Drop PDF files here or click to browse
            </h3>
            <p className="text-gray-500 mb-6">
              Select up to 50 PDF files for batch processing
            </p>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf"
              onChange={handleFileSelect}
              className="hidden"
            />
            
            <button
              onClick={() => fileInputRef.current?.click()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 mx-auto"
            >
              <Upload className="w-5 h-5" />
              Select Files
            </button>
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h4 className="text-lg font-semibold mb-4">
                Selected Files ({selectedFiles.length})
              </h4>
              
              <div className="max-h-64 overflow-y-auto space-y-2 mb-6">
                {selectedFiles.map((file) => (
                  <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="w-5 h-5 text-red-500" />
                      <div>
                        <div className="font-medium text-gray-900">{file.name}</div>
                        <div className="text-sm text-gray-500">{formatFileSize(file.size)}</div>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(file.id)}
                      className="text-red-500 hover:text-red-700 p-1"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>

              {/* Processing Options */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h5 className="font-semibold mb-3">Processing Options</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={options.extractTables}
                      onChange={(e) => setOptions(prev => ({ ...prev, extractTables: e.target.checked }))}
                      className="rounded"
                    />
                    Extract Tables
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={options.extractText}
                      onChange={(e) => setOptions(prev => ({ ...prev, extractText: e.target.checked }))}
                      className="rounded"
                    />
                    Extract Text
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={options.ocrEnabled}
                      onChange={(e) => setOptions(prev => ({ ...prev, ocrEnabled: e.target.checked }))}
                      className="rounded"
                    />
                    OCR for Scanned PDFs
                  </label>
                  <div>
                    <label className="block text-sm font-medium mb-1">Output Format:</label>
                    <select
                      value={options.outputFormat}
                      onChange={(e) => setOptions(prev => ({ ...prev, outputFormat: e.target.value as 'xlsx' | 'csv' }))}
                      className="w-full rounded border-gray-300"
                    >
                      <option value="xlsx">Excel (.xlsx)</option>
                      <option value="csv">CSV (.csv)</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 justify-center">
                <button
                  onClick={startBatchProcessing}
                  disabled={isProcessing}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                >
                  <Play className="w-5 h-5" />
                  {isProcessing ? 'Starting...' : 'Start Batch Processing'}
                </button>
                <button
                  onClick={clearFiles}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                >
                  <Trash2 className="w-5 h-5" />
                  Clear All
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        /* Progress Section */
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Clock className="w-6 h-6 text-blue-600" />
              Processing Batch: {currentBatch.name}
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{currentBatch.progress.total}</div>
                <div className="text-sm text-gray-600">Total Files</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{currentBatch.progress.completed}</div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{currentBatch.progress.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className={`text-2xl font-bold ${getStatusColor(currentBatch.status)}`}>
                  {currentBatch.status.toUpperCase()}
                </div>
                <div className="text-sm text-gray-600">Status</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{getProgressPercentage()}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    currentBatch.status === 'completed' ? 'bg-green-500' :
                    currentBatch.status === 'failed' ? 'bg-red-500' :
                    currentBatch.status === 'cancelled' ? 'bg-gray-500' :
                    'bg-blue-500'
                  }`}
                  style={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
            </div>

            {/* Current File */}
            {currentBatch.progress.currentFile && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div className="flex items-center gap-2 text-blue-800">
                  <FileText className="w-4 h-4" />
                  Processing: {currentBatch.progress.currentFile}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4 justify-center">
              {currentBatch.status === 'processing' && (
                <button
                  onClick={cancelBatch}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                >
                  <Stop className="w-5 h-5" />
                  Cancel Batch
                </button>
              )}
              {currentBatch.status === 'completed' && (
                <button
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                >
                  <Download className="w-5 h-5" />
                  Download Results
                </button>
              )}
              <button
                onClick={startNewBatch}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
              >
                <Plus className="w-5 h-5" />
                Start New Batch
              </button>
            </div>
          </div>

          {/* Results */}
          {currentBatch.results && currentBatch.results.length > 0 && (
            <div>
              <h4 className="text-lg font-semibold mb-4">Processing Results</h4>
              <div className="max-h-64 overflow-y-auto space-y-2">
                {currentBatch.results.map((result) => (
                  <div
                    key={result.fileId}
                    className={`flex items-center justify-between p-3 rounded-lg ${
                      result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {result.success ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-600" />
                      )}
                      <div>
                        <div className="font-medium">{result.fileName}</div>
                        <div className="text-sm text-gray-600">
                          {formatProcessingTime(result.processingTime)}
                          {result.complexity && ` • ${result.complexity}`}
                        </div>
                      </div>
                    </div>
                    <div>
                      {result.success && result.outputPath ? (
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center gap-1">
                          <Download className="w-4 h-4" />
                          Download
                        </button>
                      ) : (
                        <span className="text-red-600 text-sm">{result.error}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Batch History */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <History className="w-6 h-6 text-gray-600" />
          Recent Batch Jobs
        </h3>
        
        {batchHistory.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <History className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No batch jobs found. Start your first batch processing above!</p>
          </div>
        ) : (
          <div className="space-y-3">
            {batchHistory.map((batch) => (
              <div
                key={batch.id}
                onClick={() => loadBatch(batch.id)}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-all duration-200"
              >
                <div>
                  <div className="font-medium">{batch.name}</div>
                  <div className="text-sm text-gray-600 flex gap-4">
                    <span>{new Date(batch.createdAt).toLocaleDateString()}</span>
                    <span>{batch.progress.total} files</span>
                    <span className={getStatusColor(batch.status)}>{batch.status}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600 mb-1">
                    {batch.progress.completed}/{batch.progress.total}
                  </div>
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(batch.progress.completed / batch.progress.total) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BatchProcessing;
