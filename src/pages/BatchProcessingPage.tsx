import React from 'react';
import { Helmet } from 'react-helmet-async';
import BatchProcessing from '../components/BatchProcessing';
import { useAuth } from '../contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const BatchProcessingPage: React.FC = () => {
  const { user } = useAuth();

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return (
    <>
      <Helmet>
        <title>Batch Processing - PDF to Excel Converter</title>
        <meta 
          name="description" 
          content="Process multiple PDF files simultaneously with our advanced batch processing feature. Convert dozens of PDFs to Excel files efficiently." 
        />
        <meta name="keywords" content="batch processing, multiple PDF conversion, bulk PDF to Excel, batch converter" />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        {/* Navigation breadcrumb */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-6xl mx-auto px-6 py-4">
            <nav className="flex items-center space-x-2 text-sm text-gray-600">
              <a href="/dashboard" className="hover:text-blue-600 transition-colors">
                Dashboard
              </a>
              <span>/</span>
              <span className="text-gray-900 font-medium">Batch Processing</span>
            </nav>
          </div>
        </div>

        {/* Main content */}
        <div className="py-8">
          <BatchProcessing />
        </div>

        {/* Feature highlights */}
        <div className="bg-white border-t border-gray-200 py-12">
          <div className="max-w-6xl mx-auto px-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Why Use Batch Processing?
              </h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Our advanced batch processing feature is designed for efficiency and scale, 
                perfect for businesses and professionals who need to convert multiple PDFs regularly.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">Lightning Fast</h4>
                <p className="text-gray-600">
                  Process up to 50 files simultaneously with our optimized parallel processing engine.
                </p>
              </div>

              <div className="text-center">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">Smart Processing</h4>
                <p className="text-gray-600">
                  Automatic complexity detection and OCR for scanned documents ensures optimal results.
                </p>
              </div>

              <div className="text-center">
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">Real-time Tracking</h4>
                <p className="text-gray-600">
                  Monitor progress in real-time with detailed status updates and error reporting.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Usage tips */}
        <div className="bg-gray-50 py-12">
          <div className="max-w-4xl mx-auto px-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              Tips for Best Results
            </h3>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  File Preparation
                </h4>
                <ul className="space-y-2 text-gray-600">
                  <li>• Ensure PDFs are not password-protected</li>
                  <li>• Group similar document types together</li>
                  <li>• Check file sizes (max 50MB per file)</li>
                  <li>• Use descriptive filenames for easy identification</li>
                </ul>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Processing Options
                </h4>
                <ul className="space-y-2 text-gray-600">
                  <li>• Enable OCR for scanned documents</li>
                  <li>• Choose appropriate output format</li>
                  <li>• Enable table extraction for structured data</li>
                  <li>• Monitor progress and cancel if needed</li>
                </ul>
              </div>
            </div>

            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-start gap-3">
                <svg className="w-6 h-6 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="text-lg font-semibold text-blue-900 mb-2">Pro Tip</h4>
                  <p className="text-blue-800">
                    For large batches, consider processing files in smaller groups of 10-20 files 
                    for optimal performance and easier management. You can always run multiple 
                    batches simultaneously.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BatchProcessingPage;
