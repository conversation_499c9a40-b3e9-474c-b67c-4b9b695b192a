{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001 in development mode\u001b[39m","timestamp":"2025-06-19 13:06:26:626"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001 in development mode\u001b[39m","timestamp":"2025-06-19 14:54:19:5419"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUsing in-memory cache (Redis disabled)\u001b[39m","timestamp":"2025-06-19 16:26:11:2611"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUsing in-memory cache (Redis disabled)\u001b[39m","timestamp":"2025-06-19 16:26:11:2611"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001 in development mode\u001b[39m","timestamp":"2025-06-19 16:26:11:2611"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:31:29:3129"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:31:29:3129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:31:31:3131"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:31:31:3131"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:31:31:3131"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:31:31:3131"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:31:31:3131"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:31:31:3131"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:31:31:3131"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:31:31:3131"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:31:32:3132"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:31:32:3132"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:45:44:4544"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:45:44:4544"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:45:51:4551"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:45:51:4551"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLogin attempt for email: <EMAIL>\u001b[39m","timestamp":"2025-06-19 16:45:52:4552"}
{"error":{"code":"28P01","message":"password authentication failed for user \"postgres\"","stack":"error: password authentication failed for user \"postgres\"\n    at Parser.parseErrorMessage (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:285:98)\n    at Parser.handlePacket (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:122:29)\n    at Parser.parse (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/parser.js:35:38)\n    at Socket.<anonymous> (/home/<USER>/Dokumenty/excel/backend/node_modules/pg-protocol/dist/index.js:11:42)\n    at Socket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)\n    at TCP.onStreamRead (node:internal/stream_base_commons:191:23)"},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError 500: password authentication failed for user \"postgres\"\u001b[39m","request":{"ip":"::1","method":"POST","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"timestamp":"2025-06-19 16:45:52:4552"}
